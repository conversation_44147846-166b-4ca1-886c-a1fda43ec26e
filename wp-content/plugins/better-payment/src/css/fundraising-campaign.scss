// _common.scss

// layout_1

.better-payment_campaign-layout_1 {
    background-color: #fcf8fa;
    padding         : 70px 0;

    @media (max-width: 900px) {
        padding: 30px 0;
    }

    .bp-donation_thoughts-section {
        background-color: #fcf8fa;
        padding-bottom  : 85px;

        @media (max-width: 900px) {
            padding-bottom: 60px;
        }

        .bp-image_wrapper-left {
            width     : 100%;
            min-height: 1px;

            .bp-left-img-wrapper{
               margin-top: 75px;
            }
        }

        .bp-text_wrapper {
            max-width: 720px;
            margin   : 0 auto;

            // bp-text_wrapper responsive 1200 px

            .bp-text_primary {
                margin-bottom: 32px;

                // bp-text_primary responsive 1200 px
                @media (max-width: 1200px) {
                    font-size    : 46px;
                    margin-bottom: 22px;
                }

                // bp-text_primary responsive 900 px
                @media (max-width: 900px) {
                    font-size    : 40px;
                    margin-bottom: 20px;
                    padding      : 0 40px;
                }

                // bp-text_primary responsive 900 px
                @media (max-width: 500px) {
                    font-size: 36px;
                    padding  : 0;
                }
            }

            .bp-text_secondary {
                margin-bottom: 32px;

                // bp-text_secondary responsive 1200 px
                @media (max-width: 1200px) {
                    margin-bottom: 22px;
                }

                // bp-text_secondary responsive 900 px
                @media (max-width: 900px) {
                    margin-bottom: 20px;
                    padding      : 0 40px;
                }

                // bp-text_secondary responsive 500 px
                @media (max-width: 500px) {
                    margin-bottom: 20px;
                    padding      : 0;
                }
            }

            .bp-share_box {
                span {
                    font-size               : 18px;
                    font-weight             : 500;
                    text-decoration-skip-ink: none;
                    color                   : #1D2939;
                }

                .bp-social_media {
                    width : 40px;
                    height: 40px;
                    margin: 10px;

                    @media (max-width: 900px) {
                        width : 30px;
                        height: 30px;
                        margin: 5px 5px 25px 5px;
                    }
                }
            }
        }

        .bp-image_wrapper-right {
            width     : 100%;
            min-height: 1px;

            .bp-right-img-wrapper{
               margin-top: 65px;
            }
        }
    }


    .bp-charity_raised-section {
        margin-bottom: 50px;

        .bp-charity_raised-card {
            background-color: #F8F2FF;
            border          : 1px solid #F2EBFB;
            border-radius   : 20px;
            overflow        : hidden;

            .bp-form_wrapper {
                padding       : 40px 60px 40px 16px;
                display       : flex;
                flex-direction: column;
                height        : 100%;

                // bp-form_wrapper responsive 1200 px
                @media (max-width: 1200px) {
                    padding: 10px 10px 10px 0px;
                }

                // bp-form_wrapper responsive 1200 px
                @media (max-width: 900px) {
                    padding: 20px;
                }




                .bp-form_header {
                    margin-bottom: 32px;

                    @media (max-width: 1200px) {
                        margin-bottom: 22px;
                    }

                    @media (max-width: 900px) {
                        margin-bottom: 10px;
                    }
                }

                form {
                    .bp-form_top {
                        margin-bottom: 6px;

                        span {
                            margin: 5px;
                        }

                        .bp-progress_output {
                            color: #4B5D7B;
                        }
                    }

                    /* Styling the progress bar container */
                    .bp-progress_bar {
                        appearance      : none;
                        width           : 100%;
                        height          : 12px;
                        background-color: #EADDF8;
                        border-radius   : 9px;
                        overflow        : hidden;
                        margin-bottom   : 20px;

                        @media (max-width: 1200px) {
                            margin-bottom: 12px;
                        }
                    }

                    /* For webkit browsers (Chrome, Safari) */
                    .bp-progress_bar::-webkit-progress-bar {
                        background-color: #EADDF8;
                        border-radius   : 6px;
                    }

                    .bp-progress_bar::-webkit-progress-value {
                        background-color: #B66FFF;
                        border-radius   : 6px;
                        transition      : width 0.3s ease;
                    }

                    /* For Mozilla (Firefox) */
                    .bp-progress_bar::-moz-progress-bar {
                        background-color: #B66FFF;
                        border-radius   : 6px;
                        transition      : width 0.3s ease;
                    }

                    .bp-radio_label {
                        display         : inline-flex;
                        align-items     : center;
                        justify-content : center;
                        background-color: #F6EEFF;
                        border          : 1px solid #EDDCFF;
                        border-radius   : 8px;
                        color           : #141320;
                        outline         : none;
                        text-align      : center;
                        padding         : 20px;
                        width           : 100%;
                        /* Make it flexible for responsiveness */
                        max-width       : 128px;
                        height          : 48px;
                        font-size       : 20px;
                        font-weight     : 500;
                        cursor          : pointer;
                        transition      : all 0.3s ease;
                        margin-right    : 12px;
                        margin-bottom   : 20px;

                        input[type="radio"] {
                            display: none;
                        }

                        @media (max-width: 1200px) {
                            margin-bottom: 12px;
                        }

                        @media (max-width: 900px) {
                            padding  : 0px;
                            font-size: 16px;
                        }

                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    .bp-radio_label input[type="radio"]:checked {
                        background-color: #D3B3FF;
                        /* Highlight checked radio button */
                    }

                    .bp-radio_label:hover {
                        background-color: #F1D8FF;
                        /* Slight hover effect */
                    }

                    .bp-radio_active {
                        background-color: #F1D8FF;
                    }

                    .bp-physical_add-amount {
                        background-color: #F6EEFF;
                        border          : 1px solid #EDDCFF;
                        width           : 100%;
                        padding         : 20px;
                        border-radius   : 8px;
                        outline         : none;
                        font-size       : 17px;
                        font-weight     : 500;
                        color           : #48506D;
                        margin-bottom   : 32px;

                        @media (max-width: 1200px) {
                            margin-bottom: 22px;
                        }

                        @media (max-width: 900px) {
                            padding: 15px;
                        }

                        &::placeholder {
                            font-size  : 17px;
                            font-weight: 500;
                            color      : #48506D;
                            opacity    : 1;
                        }
                    }

                    .bp-donate_btn {
                        padding    : 20px 32px 20px 32px;
                        font-size  : 20px;
                        font-weight: 500;

                        @media (max-width: 900px) {
                            padding  : 15px 25px;
                            font-size: 15px;
                        }
                    }
                }
            }
        }
    }



    .bp-content_navigation-section {
        background-color: #fcf8fa;
        margin-bottom   : 20px;

        .bp-tab_navigation {
            gap: 60px;

            @media (max-width: 500px) {
                gap: 30px;
            }


            .bp-tab_button {
                border          : none;
                cursor          : pointer;
                transition      : background-color 0.3s;
                background-color: transparent;
                position        : relative;
                display         : inline-block;
                text-decoration : none;
                margin-bottom   : 55px;

                @media (max-width: 1200px) {
                    margin-bottom: 22px;
                }

                @media (max-width: 900px) {
                    margin-bottom: 20px;
                }

                // bp-text_primary responsive 900 px
                @media (max-width: 500px) {
                    padding: 0;
                }

                &::after {
                    content         : '';
                    position        : absolute;
                    bottom          : 0;
                    left            : 0;
                    width           : 0;
                    height          : 3px;
                    background-color: #9125FF;
                    transition      : width 0.3s ease;
                    border-radius   : 8px;
                }

                &:hover::after {
                    width        : 80%;
                    border-radius: 8px;
                }

                &:focus::after {
                    width        : 80%;
                    border-radius: 8px;

                }

                &.bp-active::after {
                    width: 80%; // Ensure active state is distinct
                }

            }

        }

        .bp-tab_content {
            .bp-tab_pane {
                display: none;

                &.bp-active {
                    display: block;

                }

                &#overview {
                    .bp-overview_text {
                        max-width    : 1081px;
                        margin-bottom: 55px;

                        @media (max-width: 1200px) {
                            margin-bottom: 22px;
                            padding      : 0;
                        }

                        @media (max-width: 900px) {
                            margin-bottom: 20px;
                        }

                        // bp-text_primary responsive 900 px
                        @media (max-width: 500px) {
                            padding: 0;
                        }
                    }

                    .bp-overview_image-wrapper {
                        margin-bottom: 55px;

                        @media (max-width: 700px) {
                            display       : flex;
                            flex-direction: column;
                            gap           : 6px;
                        }

                        .bp-overview_img-1 {
                            margin-bottom: 24px;

                            @media (max-width: 700px) {
                                margin-bottom: 0px;
                            }

                        }
                    }


                    .bp-overview_text-wrapper {
                        max-width    : 1081px;
                        margin-bottom: 55px;

                        .bp-overview_list {
                            padding: 20px 0;

                            @media (max-width: 1200px) {
                                padding: 10px;
                            }

                            .bp-overview_list-item {

                                span {
                                    margin     : 6px;
                                    line-height: 0;

                                    @media (max-width: 900px) {
                                        margin: 3px;
                                    }

                                    svg {
                                        @media (max-width: 900px) {
                                            height: 20px;
                                            width : 20px;
                                        }
                                    }
                                }

                                p {
                                    margin     : 6px;
                                    color      : #48506D;
                                    font-weight: 300;


                                    @media (max-width: 900px) {
                                        margin: 3px;
                                    }
                                }
                            }
                        }


                    }

                    .bp-overview_team-wrapper {
                        .bp-tram_section-header {
                            margin-bottom: 24px;

                            @media (max-width: 700px) {
                                margin-bottom: 20px;
                            }
                        }

                        .img-box {
                            margin-bottom: 16px;
                            max-width    : 312px;
                            margin-bottom: 16px;

                            img {
                                object-fit: cover;
                            }

                            @media (max-width: 700px) {
                                margin-bottom: 14px;
                            }
                        }

                        .member-name {
                            font-size  : 20px;
                            font-weight: 400;

                            @media (max-width: 700px) {
                                font-size: 16px;
                            }
                        }
                    }


                }

                &#comment {
                    .bp-comment_header {
                        margin-bottom: 24px;
                    }

                    .bp-comment_form {
                        background-color: #F7F0F4;
                        padding         : 12px 24px;
                        width           : 100%;
                        border-radius   : 12px;
                        margin-bottom   : 32px;

                        .bp-coomment_input {
                            width           : 100%;
                            margin-bottom   : 32px;
                            border          : none;
                            background-color: transparent;
                            padding         : 12px;
                            outline         : none;
                            font-size       : 14px;
                            font-weight     : 300;
                            resize          : none;

                            @media (max-width: 700px) {
                                padding: 0px;
                            }

                            &::placeholder {
                                font-family: 'Inter', sans-serif;
                                font-size  : 14px;
                                font-weight: 300;
                                color      : #48506D;
                            }

                        }

                        .bp-text_tools {
                            border          : none;
                            padding         : 12px;
                            background-color: #F7F0F4;
                            cursor          : pointer;
                        }

                        .bp-comment_submit {
                            padding    : 12px 16px;
                            font-size  : 14px;
                            font-weight: 500;
                        }
                    }

                    .bp-comment_box {
                        .given-comment {
                            margin-right: 9px;
                        }

                        .bp-comment_count {
                            background-color: #9125FF;
                            color           : #FCFCFD;
                            border-radius   : 7px;
                            padding         : 3px 8px;
                            display         : inline-block;
                            font-size       : 12px;
                            font-weight     : 500;
                        }

                        .bp-comment_list {
                            padding-top: 24px;

                            .bp-comment_author {
                                margin-bottom: 24px;

                                .author-img_box {
                                    margin-right: 10px;

                                    @media (max-width: 700px) {
                                        margin-right: 5px;
                                    }

                                    .author-img {
                                        width           : 48px;
                                        height          : 48px;
                                        border-radius   : 50%;
                                        background-color: #FFFFFF;
                                        line-height     : 0;
                                        overflow        : hidden;

                                        @media (max-width: 700px) {
                                            width : 38px;
                                            height: 38px;
                                        }

                                        img {
                                            object-fit: cover;
                                        }
                                    }
                                }

                                .author-info {
                                    background-color: #F7F0F4;
                                    border-radius   : 9px;
                                    margin-left     : 12px;
                                    padding         : 0px 8px;

                                    @media (max-width: 700px) {
                                        margin-left: 6px;
                                    }

                                    .author-name {
                                        margin     : 12px;
                                        font-size  : 18px;
                                        font-weight: 700;

                                        @media (max-width: 700px) {
                                            margin: 6px;
                                        }

                                    }

                                    .author-comment_date {
                                        margin     : 12px 12px 12px 4px;
                                        font-size  : 14px;
                                        font-weight: 500;
                                        color      : #48506dd6;
                                    }

                                    .author-comment {
                                        font-size  : 20px;
                                        font-weight: 300;
                                        color      : #0B0F19;
                                        padding    : 12px;

                                        @media (max-width: 700px) {
                                            font-size: 18px;
                                            padding  : 6px;
                                        }
                                    }
                                }
                            }
                        }
                    }


                }

                &#update {
                    .bp-update_box {
                        background-color: #FFFFFF;
                        padding         : 48px 72px;
                        border-radius   : 24px;

                        @media (max-width: 1200px) {
                            padding: 48px;
                        }

                        @media (max-width: 900px) {
                            padding: 30px;
                        }

                        @media (max-width: 500px) {
                            padding: 12px;
                        }

                        .bp-announcement_icon {
                            width : 84px;
                            height: 84px;
                        }

                        .bp-announcement_header {
                            font-size    : 36px;
                            color        : #252525;
                            margin-bottom: 24px;

                            @media (max-width: 900px) {
                                font-size: 30px;
                            }

                            @media (max-width: 500px) {
                                font-size: 25px;
                            }
                        }
                    }
                }
            }
        }
    }
}

//  / layout_1

// layout_2 

.better-payment_campaign-layout_2 {
    background-color: #fcf8fa;

    .bp-donation_hero-section {
        // background-image   : url('imgL2/bg_img1.png');
        background-repeat  : no-repeat;
        background-position: center;
        background-size    : cover;
        padding            : 159px 0;
        transition         : background-image 1s ease-in-out;
        margin-bottom      : 55px;
        position           : relative;
        height             : 100vh;

        @media (max-width: 1200px) {
            padding: 100px 0;
            height : auto;
        }

        @media (max-width: 900px) {
            padding      : 80px 0;
            margin-bottom: 100px;


        }

        @media (max-width: 600px) {
            height       : auto;
            margin-bottom: 50px;
        }

        @media (max-width: 500px) {
            background-image: none !important;
            background-color: #ED4961;
            padding         : 40px 0;
        }

        .bp-text_wrapper {
            max-width: 740px;
            width    : 100%;

            @media (max-width: 900px) {
                margin-bottom: 25px;
            }

            .bp-text_primary {
                margin-bottom: 40px;

                @media (max-width: 1200px) {
                    font-size    : 60px;
                    margin-bottom: 35px;
                }

                @media (max-width: 900px) {
                    margin-bottom: 30px;
                    font-size    : 46px;
                }

                @media (max-width: 500px) {
                    margin-bottom: 25px;
                    font-size    : 32px;
                }

            }

            .bp-text_xm {
                color        : #F2F4F7;
                line-height  : 1.8;
                margin-bottom: 24px;
            }

            .bp-donate_btn {
                padding: 20px 24px;

            }

        }

        .bp-progress_bar-card {
            background-color: #ffffff;
            border-radius   : 16px;
            padding         : 21px 31px;
            width           : 100%;
            margin-bottom   : 16px;

            .card-tag {
                font-size       : 16px;
                font-weight     : 600;
                color           : #D97706;
                background-color: #FFFBEB;
                padding         : 4px 12px 4px 12px;
                border-radius   : 27px;
                margin-bottom   : 10px;

            }

            .card-header {
                margin-bottom: 24px;
            }

            form {
                .bp-progress_bar {
                    width           : 100%;
                    appearance      : none; // Remove default browser styling
                    border-radius   : 9px;
                    overflow        : hidden; // Ensure rounded corners apply to the filled area
                    margin-bottom   : 12px;
                    // Background and track styling
                    background-color: #f3f3f3; // Light grey background for the track
                    height          : 20px; // Adjust height as needed
                    color           : #D6421D;

                    &::-webkit-progress-bar {
                        background-color: #f3f3f3; // Match track background color
                        border-radius   : 9px;
                    }

                    // Filled progress bar styling
                    &::-webkit-progress-value {
                        background-color: #D6421D; // Primary progress color
                        border-radius   : 9px; // Apply rounding to the filled area
                        transition      : width 0.3s ease; // Smooth transition for dynamic updates
                    }

                    &::-moz-progress-bar {
                        background-color: #D6421D;
                        border-radius   : 9px;
                        transition      : width 0.3s ease;
                    }

                    // Fallback for older browsers
                    &::-ms-fill {
                        background-color: #D6421D;
                        border-radius   : 9px;
                    }
                }

                label.bp-donations {
                    margin-bottom: 24px;
                    gap          : 3px;
                }

                .card-raised_btn {
                    width        : 48px;
                    height       : 48px;
                    border-radius: 50%;
                    border       : 1px solid #D0D5DD;

                }

            }


        }

        .bp-image_gallery-wrapper {
            .image-gallery {
                .img-wrapper {
                    margin       : 3px;
                    width        : 100%;
                    max-width    : 132px;
                    height       : 64px;
                    border-radius: 6px;
                    overflow     : hidden;
                    cursor       : pointer;
                    //  border : 2px solid #FBBF24;

                    img {
                        height    : 100%;
                        width     : 100%;
                        object-fit: cover;
                    }
                }

                .image_over-lay {
                    // content: '';
                    position        : absolute;
                    left            : 0;
                    right           : 0;
                    top             : 0;
                    bottom          : 0;
                    background-color: #00000080; // Semi-transparent red overlay
                    z-index         : 4;
                    // outline: none !important;
                }
            }
        }


        .bp-hero_overlay {
            position        : absolute;
            top             : 0;
            left            : 0;
            right           : 0;
            bottom          : 0;
            background-color: rgba(0, 0, 0, 0.4);
            z-index         : 1;

        }
    }

    .bp-content_navigation-section {
        background-color: #fcf8fa;
        padding-bottom  : 55px;

        .bp-tab_navigation {
            gap: 60px;

            @media (max-width: 500px) {
                gap: 30px;
            }


            .bp-tab_button {
                border          : none;
                cursor          : pointer;
                transition      : background-color 0.3s;
                background-color: transparent;
                position        : relative;
                display         : inline-block;
                text-decoration : none;
                margin-bottom   : 55px;

                @media (max-width: 1200px) {
                    margin-bottom: 22px;
                }

                @media (max-width: 900px) {
                    margin-bottom: 20px;
                }

                // bp-text_primary responsive 900 px
                @media (max-width: 500px) {
                    padding: 0;
                }

                &::after {
                    content         : '';
                    position        : absolute;
                    bottom          : 0;
                    left            : 0;
                    width           : 0;
                    height          : 3px;
                    background-color: #ED4961;
                    transition      : width 0.3s ease;
                    border-radius   : 8px;
                }

                &:hover::after {
                    width        : 80%;
                    border-radius: 8px;
                }

                &:focus::after {
                    width        : 80%;
                    border-radius: 8px;

                }

                &.bp-active::after {
                    width: 80%; // Ensure active state is distinct
                }

            }

        }

        .bp-tab_content {
            .bp-tab_pane {
                display: none;

                &.bp-active {
                    display: block;

                }

                &#overview {
                    .share-icon_wrapper {
                        justify-content: flex-end;
                        align-items    : flex-end;
                        height         : 100%;
                        width          : 100%;

                        @media (max-width: 900px) {
                            justify-content: center;
                            align-items    : center;
                        }

                        .share-header {
                            color        : #1D2939;
                            margin-bottom: 12px;

                            @media (max-width: 900px) {
                                width     : 100%;
                                text-align: center;
                                margin-top: 12px;
                            }
                        }

                        .bp-socal_media-wrapper {
                            gap: 20px;

                            .bp-social_media {
                                height: 40px;
                                width : 40px;

                                @media (max-width: 700px) {
                                    height: 30px;
                                    width : 30px;
                                }
                            }
                        }


                        .bp-overview_text {
                            max-width: 771px;

                            @media (max-width: 1200px) {
                                margin-bottom: 22px;
                                padding      : 0;
                            }

                            @media (max-width: 900px) {
                                margin-bottom: 20px;
                            }

                            // bp-text_primary responsive 900 px
                            @media (max-width: 500px) {
                                padding: 0;
                            }
                        }

                        .bp-overview_image-wrapper {
                            margin-bottom: 55px;

                            @media (max-width: 700px) {
                                display       : flex;
                                flex-direction: column;
                                gap           : 6px;
                            }

                            .bp-overview_img-1 {
                                margin-bottom: 24px;

                                @media (max-width: 700px) {
                                    margin-bottom: 0px;
                                }

                            }
                        }


                        .bp-overview_text-wrapper {
                            max-width    : 1081px;
                            margin-bottom: 55px;

                            .bp-overview_list {
                                padding: 20px 0;

                                @media (max-width: 1200px) {
                                    padding: 10px;
                                }

                                .bp-overview_list-item {

                                    span {
                                        margin     : 6px;
                                        line-height: 0;

                                        @media (max-width: 900px) {
                                            margin: 3px;
                                        }

                                        svg {
                                            @media (max-width: 900px) {
                                                height: 20px;
                                                width : 20px;
                                            }
                                        }
                                    }

                                    p {
                                        margin     : 6px;
                                        color      : #48506D;
                                        font-weight: 300;

                                        @media (max-width: 900px) {
                                            margin: 3px;
                                        }
                                    }
                                }
                            }


                        }
                    }
                }

                &#comment {
                    .bp-comment_header {
                        margin-bottom: 24px;
                    }

                    .bp-comment_form {
                        background-color: #F7F0F4;
                        padding         : 12px 24px;
                        width           : 100%;
                        border-radius   : 12px;
                        margin-bottom   : 32px;

                        .bp-coomment_input {
                            width           : 100%;
                            margin-bottom   : 32px;
                            border          : none;
                            background-color: transparent;
                            padding         : 12px;
                            outline         : none;
                            font-size       : 14px;
                            font-weight     : 300;
                            resize          : none;
                            transition      : border-color 0.3s ease;

                            @media (max-width: 700px) {
                                padding: 0px;
                            }

                            &::placeholder {
                                font-family: 'Inter', sans-serif;
                                font-size  : 14px;
                                font-weight: 300;
                                color      : #48506D;
                            }

                        }

                        .bp-text_tools {
                            border          : none;
                            padding         : 12px;
                            background-color: #F7F0F4;
                            cursor          : pointer;
                        }

                        .bp-comment_submit {
                            padding         : 12px 16px;
                            font-size       : 14px;
                            font-weight     : 500;
                            border-radius   : 8px;
                            background-color: #ED4961;
                            color           : #FFFFFF;
                        }
                    }

                    .bp-comment_box {
                        .given-comment {
                            margin-right: 9px;
                        }

                        .bp-comment_count {
                            background-color: #ED4961;
                            color           : #FCFCFD;
                            border-radius   : 7px;
                            padding         : 3px 8px;
                            display         : inline-block;
                            font-size       : 12px;
                            font-weight     : 500;
                        }

                        .bp-comment_list {
                            padding-top: 24px;

                            .bp-comment_author {
                                margin-bottom: 24px;

                                .author-img_box {
                                    margin-right: 10px;

                                    @media (max-width: 700px) {
                                        margin-right: 5px;
                                    }

                                    .author-img {
                                        width           : 48px;
                                        height          : 48px;
                                        border-radius   : 50%;
                                        background-color: #FFFFFF;
                                        line-height     : 0;
                                        overflow        : hidden;

                                        @media (max-width: 700px) {
                                            width : 38px;
                                            height: 38px;
                                        }

                                        img {
                                            object-fit: cover;
                                        }
                                    }
                                }

                                .author-info {
                                    background-color: #F7F0F4;
                                    border-radius   : 9px;
                                    margin-left     : 12px;
                                    padding         : 0px 8px;

                                    @media (max-width: 700px) {
                                        margin-left: 6px;
                                    }

                                    .author-name {
                                        margin     : 12px;
                                        font-size  : 18px;
                                        font-weight: 700;

                                        @media (max-width: 700px) {
                                            margin: 6px;
                                        }

                                    }

                                    .author-comment_date {
                                        margin     : 12px 12px 12px 4px;
                                        font-size  : 14px;
                                        font-weight: 500;
                                        color      : #48506dd6;
                                    }

                                    .author-comment {
                                        font-size  : 20px;
                                        font-weight: 300;
                                        color      : #0B0F19;
                                        padding    : 12px;

                                        @media (max-width: 700px) {
                                            font-size: 18px;
                                            padding  : 6px;
                                        }
                                    }
                                }
                            }
                        }
                    }


                }

                &#update {
                    .bp-update_box {
                        background-color: #FFFFFF;
                        padding         : 48px 72px;
                        border-radius   : 24px;

                        @media (max-width: 1200px) {
                            padding: 48px;
                        }

                        @media (max-width: 900px) {
                            padding: 30px;
                        }

                        @media (max-width: 500px) {
                            padding: 12px;
                        }

                        .bp-announcement_icon {
                            width : 84px;
                            height: 84px;
                        }

                        .bp-announcement_header {
                            font-size    : 36px;
                            color        : #252525;
                            margin-bottom: 24px;

                            @media (max-width: 900px) {
                                font-size: 30px;
                            }

                            @media (max-width: 500px) {
                                font-size: 25px;
                            }
                        }

                        .text-wrapper {
                            width   : 100%;
                            /* Adjust as per your layout */
                            position: relative;


                            .bp-announcement {
                                margin-bottom     : 24px;
                                display           : -webkit-box;
                                -webkit-line-clamp: 3; // Show 3 lines only
                                -webkit-box-orient: vertical;
                                overflow          : hidden;
                                text-overflow     : ellipsis;
                                transition        : all 0.3s ease-in-out;

                                &.active {
                                    display: block;
                                }
                            }

                            .bp-learn_more-btn {
                                color     : #ED4961;
                                z-index   : 9999;
                                position  : relative;
                                transition: background-color 0.3s ease;

                                .custom-icon {
                                    color: #ED4961;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .bp-donate_section {
        padding-bottom: 40px;

        .bp-donate_section-wrapper {
            background-color: #ED4961;
            border-radius   : 32px;
            padding         : 72px;

            @media (max-width: 1200px) {
                padding: 42px;
            }

            @media (max-width: 900px) {
                padding: 32px;
            }

            @media (max-width: 500px) {
                padding: 20px;
            }

            .bp-section_top-wrapper {
                margin-bottom: 132px;

                @media (max-width: 1200px) {
                    margin-bottom: 100px;
                }

                @media (max-width: 900px) {
                    margin-bottom: 80px;
                }

                @media (max-width: 500px) {
                    margin-bottom: 40px;
                }

                .bp-text_wrapper {
                    max-width: 609px;

                    @media (max-width: 900px) {
                        margin-bottom: 30px;
                    }

                    .bp-donate_section-header {
                        font-size    : 48px;
                        font-weight  : 800;
                        color        : #F9FAFB;
                        margin-bottom: 32px;

                        @media (max-width: 1200px) {
                            margin-bottom: 20px;
                            font-size    : 38px;
                        }

                        @media (max-width: 900px) {

                            font-size: 26px;
                        }

                        // @media (max-width: 500px) {

                        //     font-size    : 32px;
                        // }

                    }

                    .bp-donate_section-sub_header {
                        color        : #EAECF0;
                        font-weight  : 300;
                        margin-bottom: 24px;

                        @media (max-width: 1200px) {
                            font-size: 20px;
                        }

                        @media (max-width: 900px) {
                            font-size: 18px;
                        }
                    }

                    .bp-donate_btn {
                        padding: 20px 24px;

                        span {
                            font-size: 16px;
                        }
                    }
                }

                .img-wrapper {
                    border-radius: 20px;
                    overflow     : hidden;
                }
            }

            .bp-section_bottom-wrapper {
                .bottom-wrapper_header {
                    color        : #F9FAFB;
                    margin-bottom: 32px;
                    font-weight  : 500;

                }

                .card-wrapper {
                    background-color: #ffffff;
                    border-radius   : 12px;
                    overflow        : hidden;

                    @media (max-width: 1200px) {
                        flex-direction: column;
                        align-items   : center;
                    }

                    @media (max-width: 700px) {
                        margin-bottom : 20px;
                        flex-direction: row;
                    }


                    .card-img_box {
                        border-radius: 12px;
                        overflow     : hidden;
                        margin       : 12px;
                        max-width    : 157px;
                        min-width    : 100px;

                        @media (max-width: 1200px) {
                            width    : 100%;
                            max-width: 100%;
                            padding  : 0 12px;

                            img {
                                border-radius: 8px;
                            }
                        }

                        @media (max-width: 700px) {
                            padding: 0;
                        }
                    }

                    .card-text_box {
                        height        : 100%;
                        max-width     : 182px;
                        display       : flex;
                        flex-direction: column;
                        gap           : 10px;

                        @media (max-width: 1200px) {
                            padding  : 6px;
                            max-width: 100%;
                            width    : 100%;
                        }

                        .card-header {
                            font-size    : 18px;
                            font-weight  : 500;
                            color        : #1D2939;
                            margin-bottom: 32px;
                            max-width    : 182px;
                            height       : 50px;
                            margin       : 20px 0;

                            @media (max-width: 1200px) {
                                font-size    : 16px;
                                margin-bottom: 24px;
                                height       : auto;
                                margin       : 0;
                                max-width    : 100%;
                            }

                            @media (max-width: 900px) {
                                font-size    : 14px;
                                margin-bottom: 10px;
                            }
                        }

                        .card-btn {
                            width          : 32px;
                            height         : 32px;
                            border-radius  : 50%;
                            display        : flex;
                            justify-content: center;
                            align-items    : center;
                            border         : 0.60px solid #D0D5DD;
                            margin         : 6px;

                        }
                    }
                }

            }

        }
    }

}

// / layout_2 

.better-payment_campaign-layout_3 {

    .bp-donation_hero-section {
        margin-bottom: 80px;

        @media (max-width: 900px) {
            margin-bottom: 40px;
        }


        .bp-hero_wrapper {
            border-bottom: 2px solid #DBD8E5;
            padding      : 80px 0;

            @media (max-width: 1200px) {
                padding: 60px 0;
            }

            @media (max-width: 900px) {
                padding: 40px 0;
            }

            @media (max-width: 500px) {
                padding: 20px 0;
            }

            .bp-text_wrapper {
                max-width: 570px;
                width    : 100%;

                @media (max-width: 900px) {
                    max-width    : 100%;
                    margin-bottom: 25px;
                }


                .bp-hero_header {
                    padding-bottom: 32px;
                    border-bottom : 2px solid #DBD8E5;

                    @media (max-width: 1200px) {
                        padding-bottom: 20px;
                        font-size     : 40px;
                    }

                    @media (max-width: 900px) {
                        font-size: 50px;
                    }

                    @media (max-width: 500px) {
                        font-size: 35px;
                    }
                }

                .bp-hero_secondary-text {

                    @media (max-width: 1200px) {
                        margin-bottom: 20px;
                    }
                }

                .bp-share_icon-wrapper {
                    margin-bottom: 85px;

                    @media (max-width: 1200px) {
                        margin-bottom: 20px;
                    }

                    .share-icon_header {
                        font-size   : 18px;
                        font-weight : 500;
                        color       : #1D2939;
                        margin-right: 6px;
                    }

                    .share-icons {
                        .bp-social_media {
                            margin  : 0 6px;
                            height  : 24px;
                            width   : 24px;
                            cursor  : pointer;
                            position: relative;
                            z-index : 999;
                        }
                    }
                }


                .bp-payment_item {
                    margin-bottom: 24px;

                    @media (max-width: 1200px) {
                        justify-content: center;
                        gap            : 10px;
                    }

                    .bp-payment_item_bg {
                        background-color: #FBFBFF;
                        border          : 1px solid #DEDDE7;
                        border-radius   : 8px;
                        max-width       : 133px;
                        min-width       : 100px;
                        width           : 100%;
                        cursor          : pointer;
                        padding         : 20px 0px;
                        text-align      : center;
                        color           : #141320;

                        @media (max-width: 1200px) {
                            padding: 13px 0px;
                        }

                        // @media (max-width: 700px) {
                        //     max-width: 80px;
                        // }

                        &.active_border {
                            border          : 1px solid #4E36E9;
                            background-color: #FFFFFF;
                            color           : #2B2748;
                        }

                        .logo {
                            display        : flex;
                            justify-content: center;
                            align-items    : center;
                            width          : 100%;
                            height         : 100%;
                            width          : 100%;
                            padding        : 16px;

                            img {

                                height    : 32px;
                                object-fit: contain;
                            }
                        }
                    }

                }

                .bp-payment_form {
                    .input-fild {
                        border-bottom: 2px dashed #DBD8E5;

                        .bp-payment_form-input {
                            border            : none;
                            padding           : 0;
                            outline           : none;
                            font-size         : 56px;
                            color             : #1D2939;
                            width             : calc(100% - 45px);
                            appearance        : none;
                            -webkit-appearance: none;
                            -moz-appearance   : none;

                            @media (max-width: 900px) {
                                font-size: 40px;
                            }

                            @media (max-width: 500px) {
                                font-size: 35px;
                            }

                            &::-webkit-inner-spin-button,
                            &::-webkit-outer-spin-button {
                                appearance        : none;
                                -webkit-appearance: none;
                                margin            : 0; // Number input-এর ক্ষেত্রে তীর সরাতে
                            }

                            &::placeholder {
                                font-size: 56px;
                                color    : #1D2939;

                                @media (max-width: 900px) {
                                    font-size: 40px;
                                }

                                @media (max-width: 500px) {
                                    font-size: 35px;
                                }
                            }
                        }
                    }

                    .bp-donation_btn {
                        padding: 20px 26px;

                        @media (max-width: 900px) {
                            margin : 0 auto;
                            padding: 20px;
                        }

                        span {
                            margin: 0 6px;
                        }
                    }
                }

            }

            .bp-img_wrapper {

                width        : 100%;
                border-radius: 40px;
                overflow     : hidden;

                @media (max-width: 900px) {
                    max-width: 550px;
                    margin   : 0 auto;
                }

                img {
                    object-fit   : cover;
                    border-radius: 40px;
                }
            }
        }
    }

    .bp-help_section {
        .bp-help_wrapper {
            .bp-img_gallery {
                display              : grid;
                grid-template-columns: repeat(2, 1fr);
                grid-auto-rows       : 1fr;
                gap                  : 32px;
                margin-bottom        : 55px;
                margin-top           : 55px;

                @media (max-width: 700px) {
                    gap: 10px;
                }

                .bp-img_wrapper {
                    width        : 100%;
                    height       : auto;
                    border-radius: 12px;
                    overflow     : hidden;

                    img {
                        width     : 100%;
                        height    : 100%;
                        object-fit: cover;
                    }
                }

                .bp-img_wrapper:nth-child(3) {
                    grid-column: 1 / -1;
                }
            }


        }

        .bp-comment_wrapper {
            #comment {
                .bp-comment_header {
                    margin-bottom: 24px;
                    margin-top   : 80px;
                }

                .bp-comment_form {
                    background-color: #F7F9FA;
                    padding         : 12px 24px;
                    width           : 100%;
                    border-radius   : 12px;
                    margin-bottom   : 32px;

                    .bp-coomment_input {
                        width           : 100%;
                        margin-bottom   : 32px;
                        border          : none;
                        background-color: transparent;
                        padding         : 12px;
                        outline         : none;
                        font-size       : 14px;
                        font-weight     : 300;
                        resize          : none;

                        @media (max-width: 700px) {
                            padding: 0px;
                        }

                        &::placeholder {
                            font-family: 'Inter', sans-serif;
                            font-size  : 14px;
                            font-weight: 300;
                            color      : #48506D;
                        }

                    }

                    .bp-text_tools {
                        border          : none;
                        padding         : 12px;
                        background-color: #F7F9FA;
                        cursor          : pointer;
                    }

                    .bp-comment_submit {
                        padding    : 12px 16px;
                        font-size  : 14px;
                        font-weight: 500;
                    }
                }

                .bp-comment_box {
                    .given-comment {
                        margin-right: 9px;
                    }

                    .bp-comment_count {
                        background-color: #4E36E9;
                        color           : #FCFCFD;
                        border-radius   : 7px;
                        padding         : 3px 8px;
                        display         : inline-block;
                        font-size       : 12px;
                        font-weight     : 500;
                    }

                    .bp-comment_list {
                        padding-top: 24px;

                        .bp-comment_author {
                            margin-bottom: 24px;

                            .author-img_box {
                                margin-right: 10px;

                                @media (max-width: 700px) {
                                    margin-right: 5px;
                                }

                                .author-img {
                                    width           : 48px;
                                    height          : 48px;
                                    border-radius   : 50%;
                                    background-color: #FFFFFF;
                                    line-height     : 0;
                                    overflow        : hidden;

                                    @media (max-width: 700px) {
                                        width : 38px;
                                        height: 38px;
                                    }

                                    img {
                                        object-fit: cover;
                                    }
                                }
                            }

                            .author-info {
                                background-color: #F7F9FA;
                                border-radius   : 9px;
                                margin-left     : 12px;
                                padding         : 0px 8px;

                                @media (max-width: 700px) {
                                    margin-left: 6px;
                                }

                                .author-name {
                                    margin     : 12px;
                                    font-size  : 18px;
                                    font-weight: 700;

                                    @media (max-width: 700px) {
                                        margin: 6px;
                                    }

                                }

                                .author-comment_date {
                                    margin     : 12px 12px 12px 4px;
                                    font-size  : 14px;
                                    font-weight: 500;
                                    color      : #48506dd6;
                                }

                                .author-comment {
                                    font-size  : 20px;
                                    font-weight: 300;
                                    color      : #0B0F19;
                                    padding    : 12px;

                                    @media (max-width: 700px) {
                                        font-size: 18px;
                                        padding  : 6px;
                                    }
                                }
                            }
                        }
                    }
                }


            }
        }


        .bp-news_card {
            background-color: #F9FAFB;
            border          : 1px solid #ECF0F5;
            padding         : 32px 32px 20px 32px;
            border-radius   : 16px;
            width           : 100%;

            @media (max-width: 1200px) {
                padding      : 20px 20px 0px 20px;
                margin-bottom: 40px;
            }

            .news-card_icon {
                background-color: #FFFFFF;
                border          : 0.8px solid #EEF0F3;
                width           : 48px;
                height          : 48px;
                border-radius   : 6px;
            }

            .card-header {
                margin-bottom: 20px;
            }

            .card-sub_text {
                margin-bottom: 48px;
                max-width    : 333px;
                width        : 100%;

                @media (max-width: 900px) {
                    margin-bottom: 25px;
                }
            }

            .card-btn {
                text-align      : center;
                padding         : 16px;
                border-radius   : 12px;
                margin-bottom   : 12px;
                color           : #1D2939;
                background-color: transparent;
                transition      : all 0.3s;

                @media (max-width: 900px) {
                    padding: 12px;
                }

                &.card-btn_learn-more {
                    background-color: #4E36E9;
                    color           : #FFFFFF;

                    &:hover {
                        background-color: #3b21e4;
                    }
                }

                &.card-btn_contact-us:hover {
                    background-color: #4E36E9;
                    color           : #FFFFFF;
                }
            }
        }
    }
}


// popUp form 

.bp-popUp_form {
    .bp-payment_section {
        .bp-payment_wrapper {
            max-width       : 1171px;
            width           : 100%;
            margin          : 0 auto;
            background-color: #F5F7FA;
            padding         : 55px;
            border-radius   : 16px;

            @media (max-width: 1200px) {
                padding: 35px;
            }

            @media (max-width: 900px) {
                padding: 24px;
            }

            .bp-payment_card {
                padding         : 32px;
                background-color: #FFFFFF;
                border-radius   : 16px;

                @media (max-width: 1200px) {
                    padding: 25px;
                }

                @media (max-width: 900px) {
                    padding      : 12px;
                    margin-bottom: 24px;
                }

                .card-header {
                    margin-bottom: 8px;
                    max-width    : 247px;
                    font-weight  : 500;
                    color        : #2B2748;
                }

                .card-sub_header {
                    color        : #8088A6;
                    font-size    : 18px;
                    font-weight  : 300;
                    max-width    : 240px;
                    margin-bottom: 60px;
                }

                .title {
                    color      : #48506D;
                    font-size  : 18px;
                    font-weight: 500;
                }

                .amount {
                    font-weight: 600;
                    color      : #2B2748;
                }
            }

            .bp-payment_form-wrapper {
                padding         : 32px;
                background-color: #FFFFFF;
                border-radius   : 16px;

                @media (max-width: 1200px) {
                    padding: 25px;
                }

                .form-header {
                    font-weight  : 500;
                    margin-bottom: 24px;
                }

                .bp-author_input {

                    .bp-payment_methord {
                        margin-bottom: 24px;

                        @media (max-width: 1200px) {
                            justify-content: center;
                            gap            : 10px;
                        }

                        .bp-payment_sytem-logo_bg {
                            background-color: #F4F4F8;
                            border          : 1px solid #F4F4F8;
                            border-radius   : 8px;
                            max-width       : 130px;
                            min-width       : 100px;
                            width           : 100%;
                            cursor          : pointer;

                            &.active_border {
                                border: 1px solid #6E58F7;
                            }

                            .logo {
                                display        : flex;
                                justify-content: center;
                                align-items    : center;
                                width          : 100%;
                                height         : 100%;
                                width          : 100%;
                                padding        : 16px;

                                @media (max-width: 1200px) {
                                    padding: 13px;
                                }

                                img {

                                    height    : 32px;
                                    object-fit: contain;
                                }
                            }
                        }

                    }

                    .bp-payment_item {
                        margin-bottom: 24px;


                        @media (max-width: 1200px) {
                            justify-content: center;
                            gap            : 10px;
                        }

                        .bp-payment_item_bg {
                            background-color: #F4F4F8;
                            border          : 1px solid #F4F4F8;
                            border-radius   : 8px;
                            max-width       : 130px;
                            min-width       : 100px;
                            cursor          : pointer;
                            padding         : 20px 0px;
                            text-align      : center;
                            color           : #8088A6;

                            @media (max-width: 1200px) {
                                padding: 13px 0px;
                            }

                            &.active_border {
                                border          : 1px solid #6E58F7;
                                background-color: #FFFFFF;
                                color           : #2B2748;
                            }

                            .logo {
                                display        : flex;
                                justify-content: center;
                                align-items    : center;
                                width          : 100%;
                                height         : 100%;
                                width          : 100%;
                                padding        : 16px;

                                img {

                                    height    : 32px;
                                    object-fit: contain;
                                }
                            }
                        }

                    }



                    .inputs-header {
                        display      : block;
                        margin-bottom: 8px;
                    }

                    .bp-payment_form-input {
                        width           : 100%;
                        padding         : 19px 24px;
                        border-radius   : 8px;
                        outline         : none;
                        background-color: #ffffff;
                        border          : 2px solid #D3DBEE;
                        color           : #2B2748;
                        font-family     : 'IBM Plex Sans', sans-serif;
                        font-size       : 18px;
                        font-weight     : 500;
                        margin-bottom   : 24px;

                        @media (max-width: 900px) {
                            padding: 15px;
                        }

                        @media (max-width: 500px) {
                            padding: 12px;
                        }

                        &:focus {
                            border: 2px solid #6E58F7;

                        }

                        &::placeholder {
                            font-family: 'IBM Plex Sans', sans-serif;
                            font-size  : 18px;
                            font-weight: 500;
                            text-align : left;
                            color      : #8088A6;

                            @media (max-width: 900px) {
                                font-size: 15px;
                            }

                            @media (max-width: 500px) {
                                font-size: 13px;
                            }

                        }

                        &::-webkit-calendar-picker-indicator {
                            cursor: pointer;
                            filter: brightness(0.6);
                        }
                    }

                    .next-payment_methord {
                        margin-right    : 16px;
                        border          : 2px solid #D3DBEE;
                        padding         : 4px;
                        width           : 20px;
                        height          : 20px;
                        appearance      : none;
                        background-color: white;
                        border-radius   : 4px;
                        cursor          : pointer;
                        display         : inline-block;
                        position        : relative;

                        &::before {
                            content         : '';
                            position        : absolute;
                            top             : 2px;
                            left            : 2px;
                            width           : 12px;
                            height          : 12px;
                            background-color: #6E58F7;
                            border-radius   : 2px;
                            opacity         : 0;
                        }

                        // &:checked {
                        // }

                        &:checked::before {
                            opacity: 1;
                        }

                        &:focus {
                            outline: none;
                        }
                    }

                    .next-payment_methord-text {
                        cursor: pointer;
                        color : #333;
                    }



                }

                .bp-payment_confirm-btn {
                    background-color: #6E58F7;
                    padding         : 19px;
                    color           : #ffffff;
                    border          : none;
                    width           : 100%;
                    border-radius   : 8px;
                    margin-top      : 35px;
                    cursor          : pointer;
                }
            }
        }
    }
}


// _base.scss

//google font
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Sora:wght@100..800&family=Hanken+Grotesk:wght@100..900&family=IBM+Plex+Sans:wght@100..900&family=Manrope:wght@100..900&display=swap');

* {
    margin         : 0;
    padding        : 0;
    box-sizing     : border-box;
    text-decoration: none;
}

p {
    line-height: 1.5;
}


img {
    width : 100%;
    height: auto;
}

span {
    display: inline-block;
}

a {
    display : block;
    cursor  : pointer;
    z-index : 999;
    position: relative;
}

.bp-position_reletive {
    position: relative;
}

.bp-sora_font {
    font-family        : "Sora", serif;
    font-optical-sizing: auto;
    font-style         : normal;
}

.bp-inter_font {
    font-family        : "Inter", serif;
    font-optical-sizing: auto;
    font-style         : normal;
}

.bp-hanken_font {
    font-family        : 'Hanken Grotesk', sans-serif;
    font-optical-sizing: auto;
    font-style         : normal;
}

.bp-ibm_font {
    font-family        : 'IBM Plex Sans', sans-serif;
    font-optical-sizing: auto;
    font-style         : normal;
}

.bp-manrope_font {
    font-family        : 'Manrope', sans-serif;
    font-optical-sizing: auto;
    font-style         : normal;
}

.bp-text_align-center {
    text-align: center;
}

.bp-text_align-left {
    text-align: left;
}

.bp-text_align-right {
    text-align: right;
}

.bp-line_height-0 {
    line-height: 0;
}

.bp-flex {
    display: flex;
}



.bp-justify_content-center {
    justify-content: center;
}

.bp-align_items-center {
    align-items: center;
}

.bp-justify_content-space_between {
    justify-content: space-between;
}

.bp-display_none {
    display: none;
}

.bp-flex_wrap {
    flex-wrap: wrap;
}


@media (max-width: 900px) {
    .bp-col_100-active_900 {
        flex     : 0 0 100%;
        max-width: 100%;
        padding  : 0 12px;
    }

.bp-left_right-image_style-active_900 {
        display: none;
    }

    .bp-d_none-ctive_900 {
        display: none;
    }
}

@media (max-width: 700px) {
    .bp-justify_content-center_active-700{
        justify-content: center;
    }
    .bp-flex_direction-reverse_active-700 {
        flex-direction: column-reverse ;
  
    }

    .bp-col_100-active_700 {
        flex     : 0 0 100%;
        max-width: 100%;
        padding  : 0 12px;
    }

    .bp-col_50-active_700 {
        flex     : 0 0 49%;
        max-width: 49%;
        padding  : 6px;

        @media (max-width: 400px) {
            padding: 3px;
        }
    }
}


// Layout -----1  font-size , button style

.better-payment_campaign-layout_1 {

    // 1. Text Primary
    .bp-text_primary {
        font-size  : 56px;
        font-weight: 600;
        color      : #1d2939;
    }

    // 2. Text Secondary
    .bp-text_secondary {
        font-size  : 24px;
        font-weight: 300;
        color      : #48506d;


        // bp-text_secondary responsive 1200 px
        @media (max-width: 1200px) {
            font-size: 20px;
        }

        // bp-text_secondary responsive 900 px
        @media (max-width: 900px) {
            font-size: 18px;
        }
    }

    .bp-text_xxl {
        font-size  : 32px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 28px;
        }

        @media (max-width: 900px) {
            font-size: 25px;
        }

        @media (max-width: 500px) {
            font-size: 23px;
        }

    }

    .bp-text_xl {
        font-size  : 28px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 24px;
        }

        @media (max-width: 900px) {
            font-size: 20px;
        }

    }

    .bp-text_l {
        font-size  : 24px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 22px;
        }

        @media (max-width: 900px) {
            font-size: 18px;
        }

        @media (max-width: 500px) {
            font-size: 16px;
        }
    }

    .bp-text_m {
        font-size  : 18px;
        font-weight: 500;
        color      : #1D2939;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .btn-1 {
        border-radius   : 8px;
        background-color: #9125FF;
        color           : #FFFFFF;
        border          : none;
        cursor          : pointer;
    }


}

.better-payment_campaign-layout_2 {

    // 1. Text Primary
    .bp-text_primary {
        font-size  : 64px;
        font-weight: 600;
        color      : #F9FAFB;
    }

    // 2. Text Secondary
    .bp-text_secondary {
        font-size  : 24px;
        font-weight: 300;
        color      : #48506d;


        // bp-text_secondary responsive 1200 px
        @media (max-width: 1200px) {
            font-size: 20px;
        }

        // bp-text_secondary responsive 900 px
        @media (max-width: 900px) {
            font-size: 18px;
        }
    }

    .bp-text_xxl {
        font-size  : 32px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 28px;
        }

        @media (max-width: 900px) {
            font-size: 25px;
        }

        @media (max-width: 500px) {
            font-size: 23px;
        }

    }

    .bp-text_xl {
        font-size  : 28px;
        font-weight: 400;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 24px;
        }

        @media (max-width: 900px) {
            font-size: 20px;
        }

    }

    .bp-text_l {
        font-size  : 24px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 22px;
        }

        @media (max-width: 900px) {
            font-size: 18px;
        }

        @media (max-width: 500px) {
            font-size: 16px;
        }
    }

    .bp-text_xm {
        font-size  : 20px;
        font-weight: 500;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .bp-text_m {
        font-size  : 16px;
        font-weight: 500;
        color      : #475467;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .btn-2 {
        border-radius   : 1000px;
        background-color: #FFFFFF;
        color           : #344054;
        border          : none;
        cursor          : pointer;
        font-weight     : 500;
    }




}

.better-payment_campaign-layout_3 {

    // 1. Text Primary
    .bp-text_primary {
        font-size  : 56px;
        font-weight: 500;
        color      : #1D2939;
    }

    // 2. Text Secondary
    .bp-text_secondary {
        font-size  : 20px;
        font-weight: 300;
        color      : #48506D;


        // bp-text_secondary responsive 1200 px
        @media (max-width: 1200px) {
            font-size: 18px;
        }

        // bp-text_secondary responsive 900 px
        @media (max-width: 900px) {
            font-size: 16px;
        }
    }

    .bp-text_xxl {
        font-size  : 40px;
        font-weight: 500;
        color      : #1D2939;

        @media (max-width: 1200px) {
            font-size: 28px;
        }

        @media (max-width: 900px) {
            font-size: 25px;
        }

        @media (max-width: 500px) {
            font-size: 23px;
        }

    }

    .bp-text_xl {
        font-size  : 28px;
        font-weight: 600;
        color      : #2B2748;

        @media (max-width: 1200px) {
            font-size: 24px;
        }

        @media (max-width: 900px) {
            font-size: 20px;
        }

    }

    .bp-text_l {
        font-size  : 20px;
        font-weight: 500;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 22px;
        }

        @media (max-width: 900px) {
            font-size: 18px;
        }

        @media (max-width: 500px) {
            font-size: 16px;
        }
    }

    .bp-text_xm {
        font-size  : 20px;
        font-weight: 500;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .bp-text_m {
        font-size  : 16px;
        font-weight: 300;
        color      : #48506D;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .btn-3 {
        border-radius   : 12px;
        background-color: #4E36E9;
        color           : #ffffff;
        border          : none;
        cursor          : pointer;
        font-weight     : 500;
        font-size       : 18px;
    }

    .margin-bottom_32 {
        margin-bottom: 32px;

        @media (max-width: 1200px) {
            margin-bottom: 24px;
        }

        @media (max-width: 900px) {
            margin-bottom: 20px;
        }
    }


}





.bp-popUp_form {

    // 1. Text Primary
    .bp-text_primary {
        font-size  : 64px;
        font-weight: 600;
        color      : #F9FAFB;
    }

    // 2. Text Secondary
    .bp-text_secondary {
        font-size  : 24px;
        font-weight: 300;
        color      : #48506d;



        // bp-text_secondary responsive 1200 px
        @media (max-width: 1200px) {
            font-size: 20px;
        }

        // bp-text_secondary responsive 900 px
        @media (max-width: 900px) {
            font-size: 18px;
        }
    }

    .bp-text_xxl {
        font-size  : 32px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 28px;
        }

        @media (max-width: 900px) {
            font-size: 25px;
        }

        @media (max-width: 500px) {
            font-size: 23px;
        }

    }

    .bp-text_xl {
        font-size  : 28px;
        font-weight: 500;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 24px;
        }

        @media (max-width: 900px) {
            font-size: 20px;
        }

    }

    .bp-text_l {
        font-size  : 24px;
        font-weight: 600;
        color      : #1d2939;

        @media (max-width: 1200px) {
            font-size: 22px;
        }

        @media (max-width: 900px) {
            font-size: 18px;
        }

        @media (max-width: 500px) {
            font-size: 16px;
        }
    }

    .bp-text_xm {
        font-size  : 20px;
        font-weight: 500;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .bp-text_xxm {
        font-size  : 18px;
        font-weight: 500;
        color      : #2B2748;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .bp-text_m {
        font-size  : 16px;
        font-weight: 500;
        color      : #475467;

        @media (max-width: 900px) {
            font-size: 15px;
        }

    }

    .btn-2 {
        border-radius   : 1000px;
        background-color: #FFFFFF;
        color           : #344054;
        border          : none;
        cursor          : pointer;
        font-weight     : 500;
    }
}


// _structure.scss


// Container
.bp-container {
  max-width: 1320px;
  margin   : 0 auto;
  padding  : 0 12px;
  z-index: 999;
  position: relative;
}

.bp-hero_container {
  max-width: 1460px;
  margin   : 0 auto;
  padding  : 0 12px;
  z-index: 999;
  position: relative;
}

// Row
.bp-row {
  display     : flex;
  flex-wrap   : wrap;
  margin-left : -12px;
  margin-right: -12px;
}

// Column
.bp-col {
  padding-left : 12px;
  padding-right: 12px;
  flex         : 1; // Default behavior
}

// Grid Columns (12 Columns)
@for $i from 1 through 12 {
  .bp-col_#{$i} {
    flex     : 0 0 percentage($i / 12); // Calculate column width
    max-width: percentage($i / 12);
  }
}