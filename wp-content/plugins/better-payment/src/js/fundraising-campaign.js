;(function($) {

    $(document).ready(function() {
        $(document).on('click', '.bp-tab_button', function() {
            // Remove active class from all buttons
            $('.bp-tab_button').removeClass('bp-active');

            // Add active class to the clicked button
            $(this).addClass('bp-active');

            // Get the tab to show
            var tabToShow = $(this).data('tab');

            // Hide all tab panes
            $('.bp-tab_pane').hide();

            // Show the selected tab pane
            $('#' + tabToShow).show();
        });

        function showMoreShowLess() {
            const $announcementText = $('.bp-announcement');
            const $learnMoreButtonText = $('.more-btn_text');

            if ($announcementText.hasClass('active')) {
                $announcementText.removeClass('active');
                $learnMoreButtonText.text('Learn More');
            } else {
                $announcementText.addClass('active');
                $learnMoreButtonText.text('Show Less');
            }
        }

        $(document).on('click', '.bp-learn_more-btn', function(e) {
            e.preventDefault(); // Optional: prevents default button behavior
            showMoreShowLess();
        });

        $(document).on('click', '.bp-payment_item input[name="option_amount"]', function () {
            var selectedValue = $(this).val();

            // Set the value in the input field
            $('.other_amount').val(selectedValue);

            // Manage active_border class
            $('label[for^="amount_"]').removeClass('active_border'); // remove from all
            $('label[for="' + $(this).attr('id') + '"]').addClass('active_border'); // add to selected
        });

        $(document).on('focus', '.bp-payment_form .other_amount', function () {
            $('input[name="option_amount"]').prop('checked', false);
            $('label[for^="amount_"]').removeClass('active_border');
        });

        $(document).on('change', '.bp-donate_amounts input[type="radio"]', function() {
            // Remove active class from all labels
            $('.bp-donate_amounts .bp-radio_label').removeClass('bp-radio_active');

            // Add active class to the label associated with the selected radio
            $(this).closest('.bp-radio_label').addClass('bp-radio_active');

            // Set the value in the number input field
            var amount = $(this).data('value');
            $('.bp-physical_add-amount').val(amount);
        });

        $(document).on('focus', '.bp-physical_add-amount', function () {
            // Uncheck all radio buttons
            $('input[type="radio"]').prop('checked', false);

            // Remove active class from all labels
            $('.bp-radio_label').removeClass('bp-radio_active');
        });

        function slider() {
            const galleryImages = $('.img-wrapper img');
            const bgImage = $('#bgImage');
            const imageWrappers = $('.img-wrapper');
            const overlays = $('.image_over-lay');
            const imageUrls = galleryImages.map(function() {
                return $(this).data('bg');
            }).get();

            let currentIndex = 0;
            let sliderInterval;

            const updateBackground = (index) => {
                if (!imageUrls[index]) return;

                // Set background image
                bgImage.css('background-image', `url('${imageUrls[index]}')`);

                // Update overlays and borders
                imageWrappers.each(function(i) {
                    const $wrapper = $(this);
                    const $overlay = $wrapper.find('.image_over-lay');

                    if (i === index) {
                        $overlay.hide();
                        $wrapper.css('border', '2px solid #FBBF24');
                    } else {
                        $overlay.show();
                        $wrapper.css('border', 'none');
                    }
                });

                // Toggle active class
                galleryImages.removeClass('active').eq(index).addClass('active');
            };

            const startSlider = () => {
                sliderInterval = setInterval(() => {
                    currentIndex = (currentIndex + 1) % imageUrls.length;
                    updateBackground(currentIndex);
                }, 5000);
            };

            // Manual click handler
            overlays.each(function(index) {
                $(this).on('click', function() {
                    clearInterval(sliderInterval);
                    currentIndex = index;
                    updateBackground(index);
                    startSlider();
                });
            });

            updateBackground(currentIndex);
            startSlider();
        }

        // Run slider on DOM ready
        // slider();
        $(function() {
            slider();
        });

    });

})(jQuery);