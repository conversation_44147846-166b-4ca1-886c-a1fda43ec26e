<?php

namespace Better_Payment\Lite\Traits;

use Better_Payment\Lite\Classes\Plugin_Usage_Tracker;

/**
 * Exit if accessed directly
 */
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Helper trait
 * 
 * @since 0.0.1
 */
trait Helper
{
    use Elements, ElementorHelper, WordPressHelper;

    public function bp_template_render($filePath, $variables = array(), $print = true)
    {
        $output = NULL;

        if (file_exists($filePath)) {
            // Extract the variables to a local namespace
            extract($variables);

            // Start output buffering
            ob_start();

            // Include the template file
            include $filePath;

            // End buffering and return its contents
            $output = ob_get_clean();
        }

        if ( $print ) {
			print $output;
        }
        
        return $output;
    }

	/**
     * List of allowed html tag for wp_kses
     *
	 * bp_allowed_tags
	 * @return array
	 */
	public function bp_allowed_tags() {
		return [
			'a'       => [
				'href'   => [],
				'title'  => [],
				'class'  => [],
				'rel'    => [],
				'id'     => [],
				'style'  => [],
				'target' => [],
			],
			'q'       => [
				'cite'  => [],
				'class' => [],
				'id'    => [],
			],
			'img'     => [
				'src'    => [],
				'alt'    => [],
				'height' => [],
				'width'  => [],
				'class'  => [],
				'id'     => [],
				'style'  => []
			],
			'span'    => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'dfn'     => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'time'    => [
				'datetime' => [],
				'class'    => [],
				'id'       => [],
				'style'    => [],
			],
			'cite'    => [
				'title' => [],
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'hr'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'b'       => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'p'       => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'i'       => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'u'       => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			's'       => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'br'      => [],
			'em'      => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'code'    => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'mark'    => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'small'   => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'abbr'    => [
				'title' => [],
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'strong'  => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'del'     => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'ins'     => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'sub'     => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'sup'     => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'div'     => [
				'class' => [],
				'id'    => [],
				'style' => []
			],
			'strike'  => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'acronym' => [],
			'h1'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'h2'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'h3'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'h4'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'h5'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'h6'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'button'  => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'data-paypal-info' => [],
			],
			'center'  => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'ul'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'ol'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'li'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
			],
			'table'   => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'dir'   => [],
				'align' => [],
			],
			'thead'   => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'align' => [],
			],
			'tbody'   => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'align' => [],
			],
			'tfoot'   => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'align' => [],
			],
			'th'      => [
				'class'   => [],
				'id'      => [],
				'style'   => [],
				'align'   => [],
				'colspan' => [],
				'rowspan' => [],
			],
			'tr'      => [
				'class' => [],
				'id'    => [],
				'style' => [],
				'align' => [],
			],
			'td'      => [
				'class'   => [],
				'id'      => [],
				'style'   => [],
				'align'   => [],
				'colspan' => [],
				'rowspan' => [],
			],
		];
	}

	/**
     * Optional usage tracker
     *
     * @since v1.1.1
     */
    public function start_plugin_tracking()
    {
        $tracker = Plugin_Usage_Tracker::get_instance( BETTER_PAYMENT_FILE, [
            'opt_in'       => true,
            'goodbye_form' => true,
            'item_id'      => '64e1f724b5e14edb343e'
        ] );
        $tracker->set_notice_options(array(
            'notice' => 'Want to help make <strong>Better Payment</strong> even more awesome? You can get a <strong>10% discount coupon</strong> for Pro upgrade if you allow.',
            'extra_notice' => 'We collect non-sensitive diagnostic data and plugin usage information.
            Your site URL, WordPress & PHP version, plugins & themes and email address to send you the
            discount coupon. This data lets us make sure this plugin always stays compatible with the most
            popular plugins and themes. No spam, I promise.',
        ));
        $tracker->init();
    }

	/**
	 * List of currencies not supported by paypal or stripe
	 *
	 * @since v1.3.1
	 */
	public function bp_unsupported_currencies( $method = '' ) {
		$method = sanitize_text_field( strtolower( $method ) );
		$all_currencies = [
			'paypal' => [
				'AED',
				'BGN',
				'KES',
				'NGN',
				'RON',
				'ZAR'
			],
			'stripe' => [],
		];

		return !empty( $all_currencies[ $method ] ) ? $all_currencies[ $method ] : [];
	}

	/**
	 * List of currencies supported by paystack
	 *
	 * @since v1.3.1
	 */
	public function bp_supported_currencies( $method = '' ) {
		$method = sanitize_text_field( strtolower( $method ) );
		$supported_currencies = [
			'paystack' => [
				'GHS',
				'KES',
				'NGN',
				'USD',
				'ZAR'
			],
		];

		return !empty( $supported_currencies[ $method ] ) ? $supported_currencies[ $method ] : [];
	}

	/**
     * Get currency symbols
     * 
     * @since 0.0.1
     */
    public function get_currency_symbols( $currency = 'USD' ) {
        $list = $this->get_currency_symbols_list();

        return !empty( $list[ $currency ] ) ? $list[ $currency ] : '$';
    }

	public function get_currency_symbols_list() {
		
		$list = [
            'USD' => "$",
            'EUR' => "€",
            'GBP' => "£",
            'AED' => "د.إ",
            'AUD' => "$",
            'BGN' => 'лв',
            'CAD' => "$",
            'CZK' => "Kč",
            'DKK' => "kr",
            'HKD' => "$",
            'HUF' => "ft",
            'ILS' => "₪",
            'JPY' => "¥",
            'KES' => "Ksh.",
            'MXN' => "$",
            'MYR' => "MYR",
            'NGN' => "₦",
            'NOK' => "kr",
            'NZD' => "$",
            'PHP' => "₱",
            'PLN' => "zł",
            'RUB' => "₽",
            'SGD' => "$",
            'SEK' => "kr",
            'CHF' => "CHF",
            'RON' => "lei",
            'TWD' => "$",
            'THB' => "฿",
            'TRY' => "₺",
            'ZAR' => "R",
        ];

		return $list;
	}

	/**
     * Get currency list
     *
     * @since 0.0.2
     */
    public function get_currency_list(){
        $currency_list = [
            'USD' => 'USD',
            'EUR' => 'EUR',
            'GBP' => 'GBP',
            'AED' => 'AED',
            'AUD' => 'AUD',
            'BGN' => 'лв',
            'CAD' => 'CAD',
            'CZK' => 'CZK',
            'DKK' => 'DKK',
            'HKD' => 'HKD',
            'HUF' => 'HUF',
            'ILS' => 'ILS',
            'JPY' => 'JPY',
            'KES' => 'KES',
            'MXN' => 'MXN',
            'MYR' => 'MYR',
            'NGN' => 'NGN',
            'NOK' => 'NOK',
            'NZD' => 'NZD',
            'PHP' => 'PHP',
            'PLN' => 'PLN',
            'RUB' => 'RUB',
            'SGD' => 'SGD',
            'SEK' => 'SEK',
            'CHF' => 'CHF',
            'RON' => 'RON',
            'TWD' => 'TWD',
            'THB' => 'THB',
            'ZAR' => 'ZAR'
        ];

        return $currency_list;
    }
}
