<?php

namespace Better_Payment\Lite\Admin\Elementor;

use Better_Payment\Lite\Admin\DB;
use Better_Payment\Lite\Classes\Handler;
use Better_Payment\Lite\Classes\Helper as ClassesHelper;
use Better_Payment\Lite\Traits\Helper;
use \Elementor\Controls_Manager as Controls_Manager;
use Elementor\Core\Kits\Documents\Tabs\Global_Typography;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;
use Elementor\Group_Control_Typography;
use Elementor\Plugin;
use \Elementor\Repeater;
use Elementor\Utils;
use \Elementor\Widget_Base as Widget_Base;

/**
 * Exit if accessed directly
 */
if (!defined('ABSPATH')) {
    exit;
}

/**
 * The elementor widget class
 *
 * @since 0.0.1
 */
class Fundraising_Campaign_Widget extends Widget_Base {

    use Helper;

    private $better_payment_campaign_global_settings = [];

    /**
	 * @var mixed|void
	 */
	protected $pro_enabled;

    /**
	 * Login_Register constructor.
	 * Initializing the Login_Register widget class.
	 * @inheritDoc
	 */
	public function __construct( $data = [], $args = null ) {
		parent::__construct( $data, $args );
		$this->pro_enabled       = apply_filters( 'better_payment/pro_enabled', false );

	}

    public function get_name() {
        return 'fundraising-campaign';
    }

    public function get_title() {
        return esc_html__( 'Fundraising Campaign', 'better-payment' );
    }

    public function get_icon() {
        return 'bp-icon bp-logo';
    }

    public function get_keywords() {
        return [
            'payment', 'better-payment' ,'paypal', 'stripe', 'sell', 'donate', 'transaction', 'online-transaction', 'paystack', 'fundraising', 'campaign'
        ];
    }

    public function get_custom_help_url() {
        return 'https://betterpayment.co/docs/';
    }

    public function get_style_depends() { 
        return apply_filters( 'better_payment/elementor/editor/get_style_depends', [  'fundraising-campaign-style' ] );
    }
    
    public function get_script_depends() {
        return apply_filters( 'better_payment/elementor/editor/get_script_depends', [ 'fundraising-campaign-script' ] );
    }

    protected function register_controls() {
        $this->better_payment_campaign_global_settings = DB::get_settings();

        $is_edit_mode = Plugin::instance()->editor->is_edit_mode();

        if ( $is_edit_mode && ( ! current_user_can('manage_options') ) ) {
            $this->better_payment_campaign_global_settings = [];
        }

        $this->start_controls_section(
            'better_payment_campaign_settings_general',
            [
                'label' => esc_html__( 'General', 'better-payment' ),
            ]
        );
        
        $this->add_control(
            'better_payment_campaign_layout',
            [
                'label'      => esc_html__( 'Campaign Layout', 'better-payment' ),
                'type'       => Controls_Manager::SELECT,
                'default'    => 'layout-1',
                'options'    => $this->better_payment_campaign_layouts(),
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_header_enable',
            [
                'label'        => __( 'Header', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'separator'    => 'before',
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_title_enable',
            [
                'label'        => __( 'Title', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'    => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_short_description_enable',
            [
                'label'        => __( 'Short Description', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'    => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_image_one_enable',
            [
                'label'        => __( 'Image One', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'    => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2', 'layout-3' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_image_two_enable',
            [
                'label'        => __( 'Image Two', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'    => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_image_three_enable',
            [
                'label'        => __( 'Image Three', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'    => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_overview_enable',
            [
                'label'        => __( 'Overview', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'separator'    => 'before',
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_overview_images_enable',
            [
                'label'        => __( 'Images', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'   => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_overview_description_one_enable',
            [
                'label'        => __( 'Description One', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'   => [
                    'better_payment_campaign_general_overview_enable' => 'yes'
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_overview_description_two_enable',
            [
                'label'        => __( 'Description Two', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'   => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_overview_mossion_enable',
            [
                'label'        => __( 'Our Mission', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition'   => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_footer_team_enable_layout_1',
            [
                'label'        => __( 'Our Team', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'separator'    => 'before',
                'condition'    => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_general_footer_related_campaign_enable_layout_2',
            [
                'label'        => __( 'Related Campaigns', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'separator'    => 'before',
                'condition'    => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->end_controls_section();
    
        $this->campaign_header_settings();
        $this->campaign_form_settings();
        $this->campaign_overview_settings();
        $this->campaign_our_team_settings();
        $this->related_campaign_settings();

        $this->form_style();
    }

    public function campaign_header_settings() {
        $this->start_controls_section(
            'better_payment_campaign_header_title',
            [
                'label'      => esc_html__( 'Header', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_header_enable' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_title_text',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'ai'     => [
                    'active' => false,
                ],
                'placeholder' => __( 'Campaign header title', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_short_description',
            [
                'label'       => __( 'Short Description', 'better-payment' ),
                'type'        => Controls_Manager::TEXTAREA,
                'label_block' => true,
                'ai'     => [
                    'active' => false,
                ],
                'placeholder' => __( 'Campaign header short description', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_images_heading',
            [
                'label'     => __( 'Images', 'better-payment' ),
                'type'      => \Elementor\Controls_Manager::HEADING,
            ]
        );

        $this->start_controls_tabs( 'better_payment_campaign_header_images_tabs' );

        $this->start_controls_tab(
            'better_payment_campaign_header_image_one_tab',
            [
                'label' => __( 'First', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2', 'layout-3' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_image_one',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2', 'layout-3' ],
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_campaign_header_image_two_tab',
            [
                'label' => __( 'Second', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_image_two',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2' ],
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_campaign_header_image_three_tab',
            [
                'label' => __( 'Third', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_header_image_three',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->end_controls_tab();
        $this->end_controls_tabs();

        $this->end_controls_section();
    }

    public function campaign_form_settings() {
        $this->start_controls_section(
            'better_payment_campaign_form_settings',
            [
                'label'     => esc_html__( 'Form', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_title_text_layout_1',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your form title',
                'default' => 'Charity Raised',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_title_text_layout_2',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your form title',
                'default' => 'Save Lives and Bring Hope to Children in Gaza',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_sub_title_text',
            [
                'label'       => __( 'Sub Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your form sub title',
                'default' => 'Urgent Cause',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_image',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'default' => [
                    'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/frame-1.png',
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_amount_label',
            [
                'label'       => __( 'Goal Amount Label', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'placeholder' => 'Enter your goal amount label',
                'default' => 'Goal',
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_amount',
            [
                'label'       => __( 'Goal Amount', 'better-payment' ),
                'type'        => Controls_Manager::NUMBER,
                'label_block' => true,
                'placeholder' => 'Enter your goal amount',
                'default' => 23000,
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_percentage_enable',
            [
                'label'        => __( 'Goal Percentage', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_bar_line_enable',
            [
                'label'        => __( 'Goal Bar Line', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-2' ],
                ],
            ]
        );

        $amount_list_repeater_l1 = new Repeater();
        $amount_list_repeater_l1->add_control(
            'better_payment_campaign_form_amount_list_val_layout_1',
            [
                'label' => esc_html__( 'Amount', 'better-payment' ),
                'type'  => Controls_Manager::NUMBER,
                'min'   => 1,
                'default' => 5,
                'placeholder' => 'Enter your amount',
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_amount_list_layout_1',
            [
                'label'       => esc_html__( 'Amount List', 'better-payment' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $amount_list_repeater_l1->get_controls(),
                'default'     => [
                    [
                        'better_payment_campaign_form_amount_list_val_layout_1' => 10
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_1' => 20
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_1' => 30
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_1' => 80
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_1' => 100
                    ],
                ],
                'title_field' => '<i class="{{ better_payment_campaign_form_amount_list_val_layout_1 }}" aria-hidden="true"></i> {{{ better_payment_campaign_form_amount_list_val_layout_1 }}}',
                'condition'   => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $amount_list_repeater_l3 = new Repeater();
        $amount_list_repeater_l3->add_control(
            'better_payment_campaign_form_amount_list_val_layout_3',
            [
                'label' => esc_html__( 'Amount', 'better-payment' ),
                'type'  => Controls_Manager::NUMBER,
                'min'   => 1,
                'default' => 5,
                'placeholder' => 'Enter your amount',
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_amount_list_layout_3',
            [
                'label'       => esc_html__( 'Amount List', 'better-payment' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $amount_list_repeater_l3->get_controls(),
                'default'     => [
                    [
                        'better_payment_campaign_form_amount_list_val_layout_3' => 10
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_3' => 20
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_3' => 40
                    ],
                    [
                        'better_payment_campaign_form_amount_list_val_layout_3' => 80
                    ],
                ],
                'title_field' => '<i class="{{ better_payment_campaign_form_amount_list_val_layout_3 }}" aria-hidden="true"></i> {{{ better_payment_campaign_form_amount_list_val_layout_3 }}}',
                'condition'   => [
                    'better_payment_campaign_layout' => [ 'layout-3' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_total_donation_enable',
            [
                'label'        => __( 'Total Donation', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_total_donation_label',
            [
                'label'       => __( 'Total Donation Label', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your total donation label',
                'default' => 'Donations',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_form_total_donation_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_amount_raised_enable',
            [
                'label'        => __( 'Raised Amount', 'better-payment' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'better-payment' ),
                'label_off'    => __( 'Hide', 'better-payment' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_goal_amount_raised_label',
            [
                'label'       => __( 'Raised Amount Label', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your raised amount label',
                'default' => 'Raised',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_form_goal_amount_raised_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ],
            ]
        );
        
        $this->add_control(
            'better_payment_campaign_form_placeholder_text_layout_1',
            [
                'label'       => __( 'Placeholder Text', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your placeholder text',
                'default' => 'Enter your amount',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_placeholder_text_layout_3',
            [
                'label'       => __( 'Placeholder Text', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your placeholder text',
                'default' => '0',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_layout' => [ 'layout-3' ],
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_button_text',
            [
                'label'       => __( 'Button Text', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your button text',
                'default' => 'Donate Now',
                'ai' => [
                    'active' => false,
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_form_button_link',
            [
                'label' => __( 'Button Link', 'better-payment' ),
                'type' => Controls_Manager::URL,
                'show_external' => false,
                'placeholder' => __( 'https://example.com/custom-page/', 'better-payment' ),
                'description' => __( 'Please enter the donation form page url here.', 'better-payment' ),
            ]
        );

        $this->end_controls_section();
    }

    public function campaign_overview_settings() {
        $this->start_controls_section(
            'better_payment_campaign_overview_settings',
            [
                'label'     => esc_html__( 'Overview', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_overview_enable' => 'yes'
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_title_text',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Enter your overview title',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_enable' => 'yes'
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_description_title',
            [
                'label'       => esc_html__( 'Descriptions', 'better-payment' ),
                'type'        => Controls_Manager::HEADING,
                'condition' => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_general_overview_description_one_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_description_one',
            [
                'label'       => __( 'Short Description 1', 'better-payment' ),
                'type'        => Controls_Manager::TEXTAREA,
                'label_block' => true,
                'placeholder' => 'Enter your campaign description',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_general_overview_description_one_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_description_two',
            [
                'label'       => __( 'Short Description 2', 'better-payment' ),
                'type'        => Controls_Manager::TEXTAREA,
                'label_block' => true,
                'placeholder' => 'Enter your campaign description',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_enable' => 'yes',
                    'better_payment_campaign_general_overview_description_two_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                ]
            ]
        );

        $this->add_control(
            'better_paymentcampaign_overview_images_heading',
            [
                'label'     => __( 'Images', 'better-payment' ),
                'type'      => \Elementor\Controls_Manager::HEADING,
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->start_controls_tabs( 'better_payment_campaign_overview_images_tabs' );

        $this->start_controls_tab(
            'better_payment_campaign_overview_image_one_tab',
            [
                'label' => __( 'First', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_image_one',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_campaign_overview_image_two_tab',
            [
                'label' => __( 'Second', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_image_two',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_campaign_overview_image_three_tab',
            [
                'label' => __( 'Third', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_image_three',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_images_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1', 'layout-3' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->end_controls_tab();
        $this->end_controls_tabs();

        $this->add_control(
            'better_paymentcampaign_overview_our_mission_heading',
            [
                'label'     => __( 'Our Mission', 'better-payment' ),
                'type'      => \Elementor\Controls_Manager::HEADING,
                'condition' => [
                    'better_payment_campaign_general_overview_mossion_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_our_mission_title_text',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'placeholder' => 'Our Mission',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_overview_mossion_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $our_mission_repeater = new Repeater();
        $our_mission_repeater->add_control(
            'better_payment_campaign_overview_our_mission_item',
            [
                'label' => esc_html__('Mission', 'better-payment'),
                'type'  => Controls_Manager::TEXT,
                'placeholder' => 'Mission item',
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_overview_our_mission_items',
            [
                'label'       => esc_html__( 'Missions', 'better-payment' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $our_mission_repeater->get_controls(),
                'default'     => [
                    [
                        'better_payment_campaign_overview_our_mission_item' => 'Provide nutritious meals to children in need',
                    ],
                    [
                        'better_payment_campaign_overview_our_mission_item' => 'Offer educational support and resources',
                    ],
                    [
                        'better_payment_campaign_overview_our_mission_item' => 'Create safe and nurturing environments',
                    ],
                    [
                        'better_payment_campaign_overview_our_mission_item' => 'Promote awareness and advocacy for child rights',
                    ],
                ],
                'title_field' => '{{{ better_payment_campaign_overview_our_mission_item }}}',
                'condition' => [
                    'better_payment_campaign_general_overview_mossion_enable' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                    'better_payment_campaign_general_overview_enable' => 'yes',
                ]
            ]
        );

        $this->end_controls_section();
    }

    public function campaign_our_team_settings() {
        $this->start_controls_section(
            'better_payment_campaign_our_team_settings',
            [
                'label'     => esc_html__( 'Our Team', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_footer_team_enable_layout_1' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ]
            ]
        );

        $this->add_control(
            'better_payment_campaign_footer_our_team_title_text',
            [
                'label'       => __( 'Title', 'better-payment' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => true,
                'default' => 'Our Team',
                'placeholder' => 'Our Team',
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_campaign_general_footer_team_enable_layout_1' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ]
            ]
        );

        $team_member_repeater = new Repeater();
        $team_member_repeater->add_control(
            'better_payment_campaign_team_member_name',
            [
                'label' => esc_html__('Name', 'better-payment'),
                'type'  => Controls_Manager::TEXT,
                'placeholder' => 'Annette Black',
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
            ]
        );
        $team_member_repeater->add_control(
            'better_payment_campaign_team_member_image',
            [
                'label' => __('Choose Image', 'better-payment'),
                'type' => Controls_Manager::MEDIA,
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
            ]
        );
        $this->add_control(
            'better_payment_campaign_team_members',
            [
                'label'       => esc_html__( 'Team Members', 'better-payment' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $team_member_repeater->get_controls(),
                'default'     => [
                    [
                        'better_payment_campaign_team_member_name' => 'Annette Black',
                        'better_payment_campaign_team_member_image' => [
                            'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-1.png',
                        ],
                    ],
                    [
                        'better_payment_campaign_team_member_name' => 'Darrell Steward',
                        'better_payment_campaign_team_member_image' => [
                            'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-2.png',
                        ],
                    ],
                    [
                        'better_payment_campaign_team_member_name' => 'Theresa Webb',
                        'better_payment_campaign_team_member_image' => [
                            'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-3.png',
                        ],
                    ],
                    [
                        'better_payment_campaign_team_member_name' => 'Guy Hawkins',
                        'better_payment_campaign_team_member_image' => [
                            'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-4.png',
                        ],
                    ],
                ],
                'title_field' => '{{{ better_payment_campaign_team_member_name }}}',
                'condition' => [
                    'better_payment_campaign_general_footer_team_enable_layout_1' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-1' ],
                ]
            ]
        );

        $this->end_controls_section();
    }

    public function related_campaign_settings() {
        $this->start_controls_section(
            'better_payment_campaign_related_campaign_settings',
            [
                'label'     => esc_html__( 'Related Campaigns', 'better-payment' ),
                'condition' => [
                    'better_payment_campaign_general_footer_related_campaign_enable_layout_2' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ]
            ]
        );

        $related_campaign_repeater = new Repeater();
        $related_campaign_repeater->add_control(
            'better_payment_campaign_related_campaign_id',
            [
                'label' => esc_html__('Campaign ID', 'better-payment'),
                'type'  => Controls_Manager::TEXT,
                'placeholder' => 'campaign_100_42b4249',
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
            ]
        );

        $this->add_control(
            'better_payment_campaign_related_campaigns',
            [
                'label'       => esc_html__( 'Campaigns', 'better-payment' ),
                'type'        => Controls_Manager::REPEATER,
                'fields'      => $related_campaign_repeater->get_controls(),
                'default'     => [
                    [
                        'better_payment_campaign_related_campaign_id' => '',
                    ],
                    [
                        'better_payment_campaign_related_campaign_id' => '',
                    ],
                    [
                        'better_payment_campaign_related_campaign_id' => '',
                    ],
                    [
                        'better_payment_campaign_related_campaign_id' => '',
                    ],
                ],
                'title_field' => '{{{ better_payment_campaign_related_campaign_id }}}',
                'condition' => [
                    'better_payment_campaign_general_footer_related_campaign_enable_layout_2' => 'yes',
                    'better_payment_campaign_layout' => [ 'layout-2' ],
                ]
            ]
        );

        $this->end_controls_section();
    }

    public function form_style() {
        $this->campaign_header_style();
        // $this->form_sidebar_text_style();
        // $this->form_container_style();
        // $this->form_fields_style();
        // $this->form_fields_amount_style();
        // $this->form_button_style();
    }

    public function campaign_header_style() {
        $this->start_controls_section(
            'better_payment_campaign_header_style',
            [
                'label' => esc_html__( 'Campaign Header Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        // $this->add_group_control(
        //     Group_Control_Background::get_type(),
        //     [
        //         'name'     => 'better_payment_form_sidebar_background',
        //         'types'    => [ 'classic', 'gradient' ],
        //         'selector' => '{{WRAPPER}} .better-payment .dynamic-amount-section, {{WRAPPER}} .better-payment .transaction-details-wrap, {{WRAPPER}} .better-payment .donation-image-wrap, {{WRAPPER}} .better-payment .order-details-wrap',
        //     ]
        // );

        // $this->add_responsive_control(
        //     'better_payment_form_sidebar_margin',
        //     [
        //         'label'      => esc_html__( 'Margin', 'better-payment' ),
        //         'type'       => Controls_Manager::DIMENSIONS,
        //         'size_units' => [ 'px', 'em', '%' ],
        //         'selectors'  => [
        //             '{{WRAPPER}} .better-payment .dynamic-amount-section' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .transaction-details-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .donation-image-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .order-details-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //         ],
        //         'separator'  => 'before',
        //     ]
        // );

        // $this->add_responsive_control(
        //     'better_payment_form_sidebar_padding',
        //     [
        //         'label'      => esc_html__( 'Padding', 'better-payment' ),
        //         'type'       => Controls_Manager::DIMENSIONS,
        //         'size_units' => [ 'px', 'em', '%' ],
        //         'selectors'  => [
        //             '{{WRAPPER}} .better-payment .dynamic-amount-section-inner.p-6' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
        //             '{{WRAPPER}} .better-payment .transaction-details-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
        //             '{{WRAPPER}} .better-payment .donation-image-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
        //             '{{WRAPPER}} .better-payment .order-details-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
        //         ],
        //     ]
        // );

        // $this->add_responsive_control(
        //     'better_payment_form_sidebar_border_radius',
        //     [
        //         'label'      => esc_html__( 'Border Radius', 'better-payment' ),
        //         'type'       => Controls_Manager::DIMENSIONS,
        //         'separator'  => 'before',
        //         'size_units' => [ 'px' ],
        //         'selectors'  => [
        //             '{{WRAPPER}} .better-payment .dynamic-amount-section' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .transaction-details-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .donation-image-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //             '{{WRAPPER}} .better-payment .order-details-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        //         ],
        //     ]
        // );

        // $this->add_group_control(
        //     Group_Control_Border::get_type(),
        //     [
        //         'name'     => 'better_payment_form_sidebar_border',
        //         'selector' => '{{WRAPPER}} .better-payment .dynamic-amount-section, {{WRAPPER}} .better-payment .transaction-details-wrap, {{WRAPPER}} .better-payment .donation-image-wrap, {{WRAPPER}} .better-payment .order-details-wrap',
        //     ]
        // );

        // $this->add_group_control(
        //     Group_Control_Box_Shadow::get_type(),
        //     [
        //         'name'     => 'better_payment_form_sidebar_box_shadow',
        //         'selector' => '{{WRAPPER}} .better-payment .dynamic-amount-section, {{WRAPPER}} .better-payment .transaction-details-wrap, {{WRAPPER}} .better-payment .donation-image-wrap, {{WRAPPER}} .better-payment .order-details-wrap',
        //     ]
        // );

        $this->end_controls_section();
    }

    public function form_sidebar_text_style() {
        $this->start_controls_section(
            'better_payment_form_sidebar_text_style',
            [
                'label' => esc_html__( 'Sidebar Text Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );
        
        $this->add_control(
            'better_payment_form_sidebar_text_title_style',
            [
                'label'     => __( 'Title Text', 'better-payment' ),
                'type'      => Controls_Manager::HEADING,
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_sidebar_text_title_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .order-details-wrap .title' => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_sidebar_text_title_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-title, {{WRAPPER}} .transaction-details-wrap .transaction-details-header-title, {{WRAPPER}} .order-details-wrap .title',
                'separator' => 'after',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_title_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .order-details-wrap .title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_title_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .order-details-wrap .title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_sidebar_text_sub_title_style',
            [
                'label'     => __( 'Sub-Title Text', 'better-payment' ),
                'type'      => Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro', 'layout-6-pro'],
                ],
            ],
        );

        $this->add_control(
            'better_payment_form_sidebar_text_sub_title_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-sub-title' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-paragraph' => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_sidebar_text_sub_title_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-sub-title, {{WRAPPER}} .transaction-details-wrap .transaction-details-header-paragraph',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_sub_title_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-sub-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-paragraph' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_sub_title_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-sub-title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .transaction-details-wrap .transaction-details-header-paragraph' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_sidebar_text_amount_style',
            [
                'label'     => __( 'Amount Text', 'better-payment' ),
                'type'      => Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ],
        );

        $this->add_control(
            'better_payment_form_sidebar_text_amount_title_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount a' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-text' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .order-details-wrap .total-amount-text' => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_sidebar_text_amount_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount, {{WRAPPER}} .transaction-details-wrap .total-amount-text, {{WRAPPER}} .order-details-wrap .total-amount-text',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_amount_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-text' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .order-details-wrap .total-amount-text' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_amount_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-text' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .order-details-wrap .total-amount-text' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_sidebar_text_amount_summary_style',
            [
                'label'     => __( 'Amount Summary', 'better-payment' ),
                'type'      => Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ],
        );

        $this->add_control(
            'better_payment_form_sidebar_text_amount_summary_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount-summary' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-number' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-number span' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .order-details-wrap .total-amount-number' => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_sidebar_text_amount_summary_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount-summary, {{WRAPPER}} .transaction-details-wrap .total-amount-number, {{WRAPPER}} .transaction-details-wrap .total-amount-number span, {{WRAPPER}} .order-details-wrap .total-amount-number',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_amount_summary_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount-summary' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-number' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .order-details-wrap .total-amount-number' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_amount_summary_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .dynamic-amount-section-inner .bp-dynamic-amount-section-amount-summary' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .transaction-details-wrap .total-amount-number' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .order-details-wrap .total-amount-number' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_sidebar_text_icon_style',
            [
                'label'     => __( 'Icon', 'better-payment' ),
                'type'      => Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ],
        );

        $this->add_control(
            'better_payment_form_sidebar_text_icon_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .bp-dynamic-amount-section-icon:before' => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-1', 'layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_icon_font_size',
            [
                'label'      => esc_html__( 'Font Size', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'size_units' => [ 'px', 'em', '%' ],
                'range'      => [
                    'px' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                    'em' => [
                        'min' => 1,
                        'max' => 50,
                    ],
                ],
                'selectors'  => [
                    '{{WRAPPER}} .bp-dynamic-amount-section-icon:before' => 'font-size: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-1', 'layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_icon_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .bp-dynamic-amount-section-icon-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-1', 'layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_sidebar_text_icon_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .bp-dynamic-amount-section-icon-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-1', 'layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->end_controls_section();
    }

    public function form_container_style() {
        $this->start_controls_section(
            'better_payment_form_container_style',
            [
                'label' => esc_html__( 'Form Container Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name'     => 'better_payment_form_container_background',
                'types'    => [ 'classic', 'gradient' ],
                'selector' => '{{WRAPPER}} .better-payment .form-content-section, {{WRAPPER}} .better-payment .general-form-wrap, {{WRAPPER}} .better-payment .donation-form-wrap, {{WRAPPER}} .better-payment .woo-form-wrap',
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_container_margin',
            [
                'label'      => esc_html__( 'Margin', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_container_padding',
            [
                'label'      => esc_html__( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section-inner.p-6' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .better-payment .general-form-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .better-payment .donation-form-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                    '{{WRAPPER}} .better-payment .woo-form-wrap' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_container_border_radius',
            [
                'label'      => esc_html__( 'Border Radius', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'separator'  => 'before',
                'size_units' => [ 'px' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'     => 'better_payment_form_container_border',
                'selector' => '{{WRAPPER}} .better-payment .form-content-section, {{WRAPPER}} .better-payment .general-form-wrap, {{WRAPPER}} .better-payment .donation-form-wrap, {{WRAPPER}} .better-payment .woo-form-wrap',
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name'     => 'better_payment_form_container_box_shadow',
                'selector' => '{{WRAPPER}} .better-payment .form-content-section, {{WRAPPER}} .better-payment .general-form-wrap, {{WRAPPER}} .better-payment .donation-form-wrap, {{WRAPPER}} .better-payment .woo-form-wrap',
            ]
        );

        $this->end_controls_section();
    }

    public function form_fields_style() {
        $this->start_controls_section(
            'better_payment_form_fields_style',
            [
                'label' => esc_html__( 'Form Fields Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'better_payment_form_fields_input_bg',
            [
                'label'     => __( 'Background Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .form-content-section input' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'background-color: {{VALUE}}',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_fields_input_text_color',
            [
                'label'     => __( 'Text Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .form-content-section input' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .form-content-section .payment-method-checkbox' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'color: {{VALUE}}',
                ],
            ]
        );
        
        $this->add_control(
            'better_payment_form_fields_input_placeholder_color',
            [
                'label'     => __( 'Placeholder Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .form-content-section input::placeholder' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input::placeholder' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input::placeholder' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input::placeholder' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_spacing',
            [
                'label'      => __( 'Spacing', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'default'    => [
                    'size' => '0',
                    'unit' => 'px',
                ],
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 100,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section input' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_padding',
            [
                'label'      => __( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section input' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap input' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_text_indent',
            [
                'label'      => __( 'Text Indent', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 60,
                        'step' => 1,
                    ],
                    '%'  => [
                        'min'  => 0,
                        'max'  => 30,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .form-content-section input' => 'text-indent: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .general-form-wrap input' => 'text-indent: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'text-indent: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'text-indent: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_width',
            [
                'label'      => __( 'Input Width', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 1200,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'description' => __( 'Set width for all input fields. Not applicable if the field is set to display inline (<b>Content => Form Settings => Form Fields (Repeater) => Display Inline?</b>)', 'better-payment' ),
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section input[type="text"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section input[type="email"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section input[type="number"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="text"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="email"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="number"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="text"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="email"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="number"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="text"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="email"]' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="number"]' => 'width: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_height',
            [
                'label'      => __( 'Input Height', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 1200,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section input[type="text"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section input[type="email"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section input[type="number"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="text"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="email"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap input[type="number"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="text"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="email"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input[type="number"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="text"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="email"]' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input[type="number"]' => 'height: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'        => 'better_payment_form_fields_input_border',
                'label'       => __( 'Border', 'better-payment' ),
                'placeholder' => '1px',
                'default'     => '1px',
                'selector'    => '{{WRAPPER}} .better-payment .form-content-section input, {{WRAPPER}} .better-payment .general-form-wrap input, {{WRAPPER}} .better-payment .donation-form-wrap input, {{WRAPPER}} .better-payment .woo-form-wrap input',
            ]
        );

        $this->add_control(
            'better_payment_form_fields_input_radius',
            [
                'label'      => __( 'Border Radius', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section input' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap input' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap input' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap input' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'separator'  => 'after',
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_fields_input_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .better-payment .form-content-section input, {{WRAPPER}} .better-payment .general-form-wrap input, {{WRAPPER}} .better-payment .donation-form-wrap input, {{WRAPPER}} .better-payment .woo-form-wrap input',
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name'      => 'better_payment_form_fields_input_box_shadow',
                'selector'  => '{{WRAPPER}} .better-payment .form-content-section input, {{WRAPPER}} .better-payment .general-form-wrap input, {{WRAPPER}} .better-payment .donation-form-wrap input, {{WRAPPER}} .better-payment .woo-form-wrap input',
            ]
        );

        $this->add_control(
			'better_payment_form_fields_payment_method_label',
			[
				'label'     => esc_html__( 'Payment Method', 'better-payment' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
			]
		);

        $this->start_controls_tabs( 'better_payment_form_fields_payment_amount_border_tabs' );

        $this->start_controls_tab(
            'better_payment_form_fields_payment_method_border_tab_active',
            [
                'label' => __( 'Active', 'better-payment' ),
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'        => 'better_payment_form_fields_payment_method_border_active',
                'label'       => __( 'Border', 'better-payment' ),
                'selector'    => '{{WRAPPER}} .better-payment .payment-form-layout-3 .payment-method-checkbox.active, {{WRAPPER}} .better-payment .payment-form-layout-1 .payment-method-checkbox.single-item.active, {{WRAPPER}} .better-payment .payment-form-layout-2 .payment-method-checkbox.single-item.active, {{WRAPPER}} .better-payment .general-form-wrap .payment-method-items input:checked + .payment-method-image-wrap, {{WRAPPER}} .better-payment .woo-form-wrap .payment-method-items input:checked + .payment-option',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_form_fields_payment_method_border_tab_inactive',
            [
                'label' => __( 'Inactive', 'better-payment' ),
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'        => 'better_payment_form_fields_payment_method_border_inactive',
                'label'       => __( 'Border', 'better-payment' ),
                'selector'    => '{{WRAPPER}} .better-payment .payment-form-layout-3 .payment-method-checkbox, {{WRAPPER}} .better-payment .general-form-wrap .payment-method-items .payment-method-image-wrap, {{WRAPPER}} .better-payment .woo-form-wrap .payment-method-items .payment-option',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-5-pro'],
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_control(
			'better_payment_form_fields_input_icon_label',
			[
				'label'     => esc_html__( 'Input Icon', 'better-payment' ),
				'type'      => Controls_Manager::HEADING,
				'separator' => 'before',
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
			]
		);

        $this->add_control(
            'better_payment_form_fields_input_icon_color',
            [
                'label'     => __( 'Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-left .icon'               => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-right .icon'              => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-left .icon i::before'     => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-right .icon i::before'    => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control .bp-currency-symbol'                => 'color: {{VALUE}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_icon_size',
            [
                'label'      => __( 'Size', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 300,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-left .icon'   => 'font-size: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-right .icon'  => 'font-size: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control .bp-currency-symbol'    => 'font-size: {{SIZE}}{{UNIT}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_icon_width',
            [
                'label'      => __( 'Width', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 300,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-left .icon'   => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-right .icon'  => 'width: {{SIZE}}{{UNIT}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_input_icon_height',
            [
                'label'      => __( 'Height', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 300,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-left .icon'   => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .form-content-section .control.has-icons-right .icon'  => 'height: {{SIZE}}{{UNIT}}',
                ],
                'condition' => [
                    'better_payment_form_layout!' => ['layout-4-pro', 'layout-5-pro', 'layout-6-pro'],
                ],
            ]
        );

        $this->end_controls_section();
    }

    public function form_fields_amount_style() {

        $this->start_controls_section(
            'better_payment_form_fields_amount_style',
            [
                'label' => __( 'Amount Fields Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_amount_width',
            [
                'label'      => __( 'Input Width', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 1200,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'width: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_amount_height',
            [
                'label'      => __( 'Input Height', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 1200,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'height: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'height: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_fields_amount_spacing',
            [
                'label'      => __( 'Spacing', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'default'    => [
                    'size' => '0',
                    'unit' => 'px',
                ],
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 100,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'margin-bottom: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->start_controls_tabs( 'better_payment_form_amount_tabs_button_style' );

        $this->start_controls_tab(
            'better_payment_form_fields_amount_normal',
            [
                'label' => __( 'Normal', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_form_fields_amount_normal_bg',
            [
                'label'     => __( 'Background Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'background-color: {{VALUE}}',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_fields_amount_normal_text_color',
            [
                'label'     => __( 'Text Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_form_fields_amount_selected',
            [
                'label' => __( 'Selected', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_form_fields_amount_selected_bg',
            [
                'label'     => __( 'Background Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group input[type="radio"].bp-form__control:checked ~ label' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount input:checked + .text' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount input:checked + .text' => 'background-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount input:checked + .text' => 'background-color: {{VALUE}}',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_fields_amount_selected_text_color',
            [
                'label'     => __( 'Text Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group input[type="radio"].bp-form__control:checked ~ label' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount input:checked + .text' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount input:checked + .text' => 'color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount input:checked + .text' => 'color: {{VALUE}}',
                ]
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'        => 'better_payment_form_fields_amount_border',
                'label'       => __( 'Border', 'better-payment' ),
                'placeholder' => '1px',
                'default'     => '1px',
                'selector'    => '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label, {{WRAPPER}} .better-payment .general-form-wrap .payment-amount input:checked + .text, {{WRAPPER}} .better-payment .donation-form-wrap .payment-amount input:checked + .text, {{WRAPPER}} .better-payment .woo-form-wrap .payment-amount input:checked + .text',
                'separator'   => 'before',
            ]
        );

        $this->add_control(
            'better_payment_form_fields_amount_border_radius',
            [
                'label'      => __( 'Border Radius', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_fields_amount_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label, {{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text, {{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text, {{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text',
                'separator' => 'before',
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name'      => 'better_payment_form_fields_amount_box_shadow',
                'selector'  => '{{WRAPPER}} .payment-form-layout .bp-payment-amount-wrap .bp-form__group label, {{WRAPPER}} .better-payment .general-form-wrap .payment-amount .text, {{WRAPPER}} .better-payment .donation-form-wrap .payment-amount .text, {{WRAPPER}} .better-payment .woo-form-wrap .payment-amount .text',
            ]
        );

        $this->end_controls_section();
    }

    public function form_button_style() {
        $this->start_controls_section(
            'better_payment_form_button_style',
            [
                'label' => __( 'Form Button Style', 'better-payment' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_button_width',
            [
                'label'      => __( 'Width', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 700,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'flex: none;width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'flex: none;width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'flex: none;width: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'flex: none;width: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->start_controls_tabs( 'better_payment_form_button_tabs_style' );

        $this->start_controls_tab(
            'better_payment_form_button_normal',
            [
                'label' => __( 'Normal', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_form_button_normal_bg',
            [
                'label'     => __( 'Background Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'background: {{VALUE}}',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_button_normal_text_color',
            [
                'label'     => __( 'Text Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'color: {{VALUE}} !important',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'     => 'better_payment_form_button_normal_border',
                'label'    => __( 'Border', 'better-payment' ),
                'default'  => '1px',
                'selector' => '{{WRAPPER}} .better-payment .payment-form-layout .button, {{WRAPPER}} .better-payment .general-form-wrap .button, {{WRAPPER}} .better-payment .donation-form-wrap .button, {{WRAPPER}} .better-payment .woo-form-wrap .button',
            ]
        );

        $this->add_control(
            'better_payment_form_button_normal_border_radius',
            [
                'label'      => __( 'Border Radius', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_button_normal_padding',
            [
                'label'      => __( 'Padding', 'better-payment' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'better_payment_form_button_normal_margin',
            [
                'label'      => __( 'Margin Top', 'better-payment' ),
                'type'       => Controls_Manager::SLIDER,
                'range'      => [
                    'px' => [
                        'min'  => 0,
                        'max'  => 100,
                        'step' => 1,
                    ],
                ],
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button' => 'margin-top: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button' => 'margin-top: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button' => 'margin-top: {{SIZE}}{{UNIT}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button' => 'margin-top: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'      => 'better_payment_form_button_normal_typography',
                'label'     => __( 'Typography', 'better-payment' ),
                'selector'  => '{{WRAPPER}} .better-payment .payment-form-layout .button, {{WRAPPER}} .better-payment .general-form-wrap .button, {{WRAPPER}} .better-payment .donation-form-wrap .button, {{WRAPPER}} .better-payment .woo-form-wrap .button',
                'separator' => 'before',
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name'      => 'better_payment_form_button_normal_box_shadow',
                'selector'  => '{{WRAPPER}} .better-payment .payment-form-layout .button, {{WRAPPER}} .better-payment .general-form-wrap .button, {{WRAPPER}} .better-payment .donation-form-wrap .button, {{WRAPPER}} .better-payment .woo-form-wrap .button',
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'better_payment_form_button_hover',
            [
                'label' => __( 'Hover', 'better-payment' ),
            ]
        );

        $this->add_control(
            'better_payment_form_button_hover_bg',
            [
                'label'     => __( 'Background Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button:hover' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .payment-form-layout.payment-form-layout-3 .button:hover' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button:hover' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button:hover' => 'background-color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button:hover' => 'background-color: {{VALUE}} !important',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_button_hover_text_color',
            [
                'label'     => __( 'Text Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button:hover' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .payment-form-layout.payment-form-layout-3 .button:hover' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button:hover' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button:hover' => 'color: {{VALUE}} !important',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button:hover' => 'color: {{VALUE}} !important',
                ],
            ]
        );

        $this->add_control(
            'better_payment_form_button_hover_border',
            [
                'label'     => __( 'Border Color', 'better-payment' ),
                'type'      => Controls_Manager::COLOR,
                'default'   => '',
                'selectors' => [
                    '{{WRAPPER}} .better-payment .payment-form-layout .button:hover' => 'border-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .general-form-wrap .button:hover' => 'border-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .donation-form-wrap .button:hover' => 'border-color: {{VALUE}}',
                    '{{WRAPPER}} .better-payment .woo-form-wrap .button:hover' => 'border-color: {{VALUE}}',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->end_controls_section();
    }

    /**
     * Render the widget output on the frontend.
     *
     * @since 1.0.0
     */
    protected function render() {
        $is_edit_mode = Plugin::instance()->editor->is_edit_mode();

        if ( $is_edit_mode && ( ! current_user_can('manage_options') ) ) {
            return;
        }
        
        $settings = $this->get_settings_for_display();

        $payment_field = "number" ;

        // $response = Handler::manage_response( $settings, $this->get_id() );
        // if ( $response ) {
        //     return false;
        // }

        // do_action('better_payment/elementor/editor/manage_response_webhook', $this, $settings );

        wp_enqueue_script( 'fundraising-campaign-script' );

        // if( $this->pro_enabled ){
        //     wp_enqueue_script( 'better-payment-pro-common-script' );
        //     wp_enqueue_style( 'better-payment-pro-common-style' );
        // }

        $action       = esc_url( admin_url( 'admin-post.php' ) );
        $setting_meta = wp_json_encode( [
            'page_id'   => get_the_ID(),
            'widget_id' => esc_attr( $this->get_id() ),
        ] );
        
        $better_payment_placeholder_class = '';
        if ( !empty($settings[ 'better_payment_placeholder_switch' ]) && $settings[ 'better_payment_placeholder_switch' ] != 'yes' ) {
            $better_payment_placeholder_class = 'better-payment-hide-placeholder';
        }        

        $data = array(
            'payment_field' => $payment_field,
            'action'       => $action,
            'setting_meta' => $setting_meta,
            'better_payment_placeholder_class' => $better_payment_placeholder_class,
        );

        $widgetObj = $this;
        $extraDatas = $data;

        $better_payment_campaign_layout = sanitize_text_field($settings[ 'better_payment_campaign_layout' ]);
        $better_payment_campaign_layout = in_array($better_payment_campaign_layout, array_keys( $this->better_payment_campaign_layouts() ) ) ? $better_payment_campaign_layout : 'layout-1';

        $template_file = BETTER_PAYMENT_ADMIN_VIEWS_PATH . '/elementor/fundraising-campaign/' . $better_payment_campaign_layout . '.php';
        $is_pro_layout = str_contains($better_payment_campaign_layout, '-pro');
        
        if ( $this->pro_enabled && $is_pro_layout ){
            $template_file = BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . '/elementor/layouts/' . $better_payment_campaign_layout . '.php';
        }

        if ( ( ! $this->pro_enabled ) && $is_pro_layout ){
            $template_file = BETTER_PAYMENT_ADMIN_VIEWS_PATH . '/partials/layout-pro-banners.php';
        }

        $better_payment_campaign_content = '';
        
        if ( file_exists($template_file) ) {
            ob_start();
            include $template_file;
            $better_payment_campaign_content = ob_get_contents();
            ob_end_clean();
        }

        $better_payment_campaign_content = apply_filters( 'better_payment/elementor/editor/get_layout_content', $better_payment_campaign_content, $settings, $this, $data );

        echo $better_payment_campaign_content;
    }

    public function better_payment_campaign_layouts(){
        $layouts = apply_filters('better_payment/elementor/widget/campaign_layouts', [
            'layout-1' => 'Layout 1',
            'layout-2' => 'Layout 2',
            'layout-3' => 'Layout 3'
        ]);

        return $layouts;
    }
}
