<?php 
    include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/campaign-vars.php";
?>
<main class="better-payment">
    <div class="better-payment_campaign-layout_1">
        <?php if( $layout_header_enable ): ?>
            <section class="bp-section-header bp-donation_thoughts-section">
                <div class="bp-hero_container">
                    <div class="bp-row ">
                        <div class="bp-col bp-col_2 bp-col_100-active_900">
                            <div class="bp-image_wrapper-left bp-text_align-center bp-left_right-image_style-active_900">
                                <?php if( $header_image_one_enable ): ?>
                                    <div class="bp-left-img-wrapper">
                                        <img src="<?php echo esc_url( $header_image_one_url ); ?>" alt="Group One" />
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="bp-col bp-col_8 bp-col_100-active_900">
                            <div class="bp-text_wrapper">
                                <?php if( $header_title_enable ): ?>
                                    <h1 class="bp-text_primary bp-sora_font bp-text_align-center">
                                        <?php echo esc_html( $header_title ); ?>
                                    </h1>
                                <?php endif; ?>
                                <?php if( $header_short_desc_enable ): ?>
                                    <p class="bp-text_secondary bp-inter_font bp-text_align-center">
                                        <?php echo esc_html( $header_short_desc ); ?>
                                    </p>
                                <?php endif; ?>
                                <!-- <div class="bp-share_box bp-text_align-center">
                                    <span class=" bp-inter_font">Share</span>
                                    <div class="bp-flex bp-justify_content-center">
                                        <a href="#" class="bp-social_media">
                                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="20" cy="20" r="20" fill="#0A66C2" />
                                                <path
                                                    d="M30.8286 29.1664H26.6876V22.6701C26.6876 21.121 26.66 19.1268 24.5339 19.1268C22.3771 19.1268 22.0471 20.8145 22.0471 22.5573V29.1659H17.9062V15.8069H21.8815V17.6325H21.9372C22.7466 16.2462 24.2572 15.4151 25.8594 15.4747C30.0564 15.4747 30.8303 18.2401 30.8303 21.8379L30.8286 29.1664Z"
                                                    fill="white" />
                                                <path
                                                    d="M13.2356 13.9809C13.2353 13.9809 13.2352 13.9809 13.2351 13.9809C11.9168 13.9809 10.832 12.8944 10.832 11.5738C10.832 10.2532 11.9168 9.1665 13.2351 9.1665C14.5532 9.1665 15.6379 10.2529 15.6382 11.5733C15.6382 11.5734 15.6382 11.5735 15.6382 11.5738C15.6382 12.8941 14.5536 13.9808 13.2356 13.9809Z"
                                                    fill="white" />
                                                <path d="M15.3054 29.1666H11.1602V15.8071H15.3054V29.1666Z" fill="white" />
                                            </svg>

                                        </a>
                                        <a href="#" class="bp-social_media">
                                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="20" cy="20" r="20" fill="#1877F2" />
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M27.7861 25.7805L28.6729 19.999H23.1257V16.2472C23.1257 14.6655 23.9005 13.1237 26.3852 13.1237H28.9073V8.20168C28.9073 8.20168 26.6183 7.81104 24.43 7.81104C19.8613 7.81104 16.8753 10.5799 16.8753 15.5926V19.999H11.7969V25.7805H16.8753V39.7568C17.8936 39.9166 18.9373 39.9998 20.0005 39.9998C21.0637 39.9998 22.1074 39.9166 23.1257 39.7568V25.7805H27.7861Z"
                                                    fill="white" />
                                            </svg>

                                        </a>
                                        <a href="#" class="bp-social_media">
                                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="20" cy="20" r="20" fill="url(#paint0_linear_1428_1338)" />
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M20.0013 6.66602C16.3802 6.66602 15.9261 6.68136 14.504 6.74625C13.0848 6.81098 12.1156 7.0364 11.2675 7.36602C10.3907 7.70671 9.64711 8.16263 8.90582 8.90386C8.16458 9.64516 7.70867 10.3887 7.36798 11.2655C7.03835 12.1136 6.81294 13.0829 6.74821 14.502C6.68332 15.9242 6.66797 16.3782 6.66797 19.9994C6.66797 23.6205 6.68332 24.0745 6.74821 25.4967C6.81294 26.9158 7.03835 27.8851 7.36798 28.7332C7.70867 29.61 8.16458 30.3535 8.90582 31.0948C9.64711 31.8361 10.3907 32.292 11.2675 32.6327C12.1156 32.9623 13.0848 33.1877 14.504 33.2524C15.9261 33.3173 16.3802 33.3327 20.0013 33.3327C23.6224 33.3327 24.0765 33.3173 25.4986 33.2524C26.9178 33.1877 27.887 32.9623 28.7351 32.6327C29.6119 32.292 30.3555 31.8361 31.0968 31.0948C31.838 30.3535 32.2939 29.61 32.6347 28.7332C32.9643 27.8851 33.1897 26.9158 33.2544 25.4967C33.3193 24.0745 33.3346 23.6205 33.3346 19.9994C33.3346 16.3782 33.3193 15.9242 33.2544 14.502C33.1897 13.0829 32.9643 12.1136 32.6347 11.2655C32.2939 10.3887 31.838 9.64516 31.0968 8.90386C30.3555 8.16263 29.6119 7.70671 28.7351 7.36602C27.887 7.0364 26.9178 6.81098 25.4986 6.74625C24.0765 6.68136 23.6224 6.66602 20.0013 6.66602ZM20.0013 9.06836C23.5614 9.06836 23.9831 9.08196 25.3891 9.14611C26.689 9.20539 27.395 9.4226 27.8649 9.6052C28.4872 9.84707 28.9314 10.136 29.3979 10.6026C29.8645 11.0691 30.1535 11.5133 30.3953 12.1357C30.5779 12.6055 30.7952 13.3115 30.8544 14.6115C30.9186 16.0174 30.9322 16.4392 30.9322 19.9993C30.9322 23.5594 30.9186 23.9812 30.8544 25.3871C30.7952 26.6871 30.5779 27.3931 30.3953 27.8629C30.1535 28.4853 29.8645 28.9294 29.3979 29.396C28.9314 29.8626 28.4872 30.1515 27.8649 30.3934C27.395 30.576 26.689 30.7932 25.3891 30.8525C23.9833 30.9166 23.5617 30.9302 20.0013 30.9302C16.4408 30.9302 16.0192 30.9166 14.6134 30.8525C13.3135 30.7932 12.6075 30.576 12.1376 30.3934C11.5153 30.1515 11.0711 29.8626 10.6046 29.396C10.138 28.9294 9.84902 28.4853 9.60715 27.8629C9.42455 27.3931 9.20734 26.6871 9.14806 25.3871C9.08391 23.9812 9.07031 23.5594 9.07031 19.9993C9.07031 16.4392 9.08391 16.0174 9.14806 14.6115C9.20734 13.3115 9.42455 12.6055 9.60715 12.1357C9.84902 11.5133 10.138 11.0691 10.6046 10.6026C11.0711 10.136 11.5153 9.84707 12.1376 9.6052C12.6075 9.4226 13.3135 9.20539 14.6134 9.14611C16.0194 9.08196 16.4411 9.06836 20.0013 9.06836Z"
                                                    fill="white" />
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M20.0031 24.4447C17.5484 24.4447 15.5586 22.4549 15.5586 20.0003C15.5586 17.5457 17.5484 15.5558 20.0031 15.5558C22.4576 15.5558 24.4475 17.5457 24.4475 20.0003C24.4475 22.4549 22.4576 24.4447 20.0031 24.4447ZM20.0031 13.1533C16.2217 13.1533 13.1562 16.2187 13.1562 20.0002C13.1562 23.7816 16.2217 26.847 20.0031 26.847C23.7845 26.847 26.8499 23.7816 26.8499 20.0002C26.8499 16.2187 23.7845 13.1533 20.0031 13.1533Z"
                                                    fill="white" />
                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                    d="M28.7195 12.8823C28.7195 13.7659 28.0032 14.4822 27.1195 14.4822C26.2359 14.4822 25.5195 13.7659 25.5195 12.8823C25.5195 11.9986 26.2359 11.2822 27.1195 11.2822C28.0032 11.2822 28.7195 11.9986 28.7195 12.8823Z"
                                                    fill="white" />
                                                <defs>
                                                    <linearGradient id="paint0_linear_1428_1338" x1="20" y1="60" x2="60"
                                                        y2="20" gradientUnits="userSpaceOnUse">
                                                        <stop stop-color="#FFD522" />
                                                        <stop offset="0.497382" stop-color="#F1000B" />
                                                        <stop offset="1" stop-color="#B900B3" />
                                                    </linearGradient>
                                                </defs>
                                            </svg>

                                        </a>
                                        <a href="#" class="bp-social_media">
                                            <svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="20" cy="20" r="20" fill="#E60023" />
                                                <path
                                                    d="M20.4393 7.41699C27.3727 7.41699 32.766 12.3603 32.766 18.967C32.766 25.8603 28.421 31.407 22.3877 31.407C20.3627 31.407 18.4543 30.357 17.8043 29.1103L16.5577 33.8653C16.2194 35.1683 15.446 36.7237 14.7373 37.98L14.4167 38.5366L14.1161 39.0386C13.6342 38.8733 13.1606 38.6902 12.697 38.4886L12.6472 37.9935C12.5052 36.445 12.4281 34.3993 12.781 32.887L15.1243 22.9453L15.0432 22.7614L14.9584 22.5402L14.893 22.3489L14.8243 22.1245L14.7556 21.8687C14.7444 21.8236 14.7332 21.7772 14.7223 21.7296L14.6598 21.43C14.5811 21.0122 14.5243 20.5222 14.5243 19.9787C14.5243 17.1953 16.1377 15.1203 18.1443 15.1203C19.851 15.1203 20.6743 16.4003 20.6743 17.937C20.6743 18.6727 20.4738 19.5645 20.2003 20.5305L20.0086 21.1846L19.4897 22.8804C19.3175 23.4535 19.1535 24.0299 19.0193 24.5953C18.5477 26.587 20.016 28.212 21.9793 28.212C25.5327 28.212 28.266 24.462 28.266 19.052C28.266 14.2653 24.8243 10.9153 19.911 10.9153C14.2193 10.9153 10.8777 15.1853 10.8777 19.5953C10.8777 21.3153 11.541 23.1587 12.3677 24.162C12.531 24.357 12.5543 24.532 12.506 24.7337L11.951 27.0003C11.8627 27.367 11.661 27.4437 11.2843 27.267C8.78432 26.1053 7.22266 22.4503 7.22266 19.517C7.22266 13.2103 11.806 7.41699 20.4393 7.41699Z"
                                                    fill="white" />
                                            </svg>

                                        </a>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                        <div class="bp-col bp-col_2 bp-col_100-active_900">
                            <div class="bp-image_wrapper-right bp-text_align-center bp-left_right-image_style-active_900">
                                <?php if( $header_image_two_enable ): ?>
                                    <div class="bp-right-img-wrapper">
                                        <img src="<?php echo esc_url( $header_image_two_url ); ?>" alt="Group Two" />
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <section class="bp-section-donate bp-charity_raised-section">
            <div class="bp-container">
                <div class="bp-charity_raised-card">
                    <div class="bp-row">
                        <div class="bp-col bp-col_5 bp-col_100-active_900">
                            <?php if( ! empty( $form_image ) ): ?>
                                <div class="bp-line_height-0">
                                    <img src="<?php echo esc_url( $form_image ); ?>" alt="Payment form" />
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="bp-col bp-col_7 bp-col_100-active_900">
                            <div class="bp-form_wrapper bp-justify_content-center">
                                <?php if( ! empty( $form_title_text ) ): ?>
                                    <h2 class="bp-form_header bp-text_xxl bp-text_align-left bp-sora_font">
                                        <?php echo esc_html( $form_title_text ); ?>
                                    </h2>
                                <?php endif; ?>

                                <form id="myForm">
                                    <div class="bp-flex bp-justify_content-space_between bp-align_items_center bp-form_top">
                                        <?php if( ! empty( $form_goal_label ) && ! empty( $form_goal_amount ) ): ?>
                                            <label for="bpProgressBar" class="bp-flex bp-text_m  bp-inter_font">
                                                <span><?php echo esc_html( $form_goal_label ); ?>:</span> <span style="color: #9125FF;"><?php echo esc_html( $form_goal_amount ); ?></span>
                                            </label>
                                        <?php endif; ?>
                                        <?php if( $form_goal_percentage_enable ): ?>
                                            <div>
                                                <output id="bpProgressOutput" class="bp-progress_output bp-text_m  bp-inter_font" aria-live="polite">0%</output>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <?php if( $form_goal_bar_line_enable ): ?>
                                        <progress id="bpProgressBar" class="bp-progress_bar" value="0"
                                            max="100">0%</progress>
                                    <?php endif; ?>

                                    <div class="bp-flex bp-align_items_center bp-donate_amounts" id="bpDonateAmount">
                                        <?php if( ! empty( $amount_items ) ) : ?>
                                            <?php foreach( $amount_items as $amount_item ) : ?>
                                                <?php if( !empty( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ) ): ?>
                                                    <label for="amount_<?php echo esc_attr( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ); ?>" class="bp-radio_label bp-inter_font">
                                                        <input type="radio" id="amount_<?php echo esc_attr( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ); ?>" name="option_amount" value="<?php echo esc_attr( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ); ?>" data-value="<?php echo esc_attr( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ); ?>">
                                                        $<?php echo esc_html( $amount_item['better_payment_campaign_form_amount_list_val_layout_1'] ); ?>
                                                    </label>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>

                                    <input type="number" class="bp-physical_add-amount bp-inter_font"
                                        placeholder="<?php echo esc_html__( $placeholder_text, 'better-payment' ); ?>">
                                    <div class="bp-flex">
                                        <a href="<?php echo esc_url( $button_link ); ?>" target="_blank" class="btn-1 bp-donate_btn bp-inter_font">
                                            <?php echo esc_html_e( $button_text, 'better-payment' ); ?>
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="bp-content_navigation-section">
            <div class="bp-container">
                <!-- Tab navigation -->
                <div class="bp-tab_navigation bp-flex">
                    <?php if( $layout_overview_enable ): ?>
                        <button class="bp-tab_button bp-text_xl bp-text_align-left bp-hanken_font  bp-active" data-tab="overview">
                            <?php echo esc_html( $overview_title ); ?>
                        </button>
                    <?php endif; ?>
                    <button class="bp-tab_button bp-text_xl bp-hanken_font " data-tab="comment">Comment</button>
                    <button class="bp-tab_button bp-text_xl bp-hanken_font " data-tab="update">Update</button>
                </div>

                <!-- Tab content -->
                <div class="bp-tab_content">
                    <?php if( $layout_overview_enable || $team_enable ): ?>
                        <div class="bp-tab_pane bp-active " id="overview">
                            <?php if( $layout_overview_enable && ($overview_desc_one_enable || $overview_images_enable || $overview_desc_two_enable || $overview_mission_enable ) ): ?>
                                <div class="bp-section-body">
                                    <?php if( $overview_desc_one_enable ): ?>
                                        <p class="bp-text_secondary bp-inter_font bp-overview_text">
                                            <?php echo esc_html( $overview_desc_one ); ?>
                                        </p>
                                    <?php endif; ?>
                                    <?php if( $overview_images_enable ): ?>
                                        <div class="bp-overview_image-wrapper bp-row">
                                            <div class="bp-col bp-col_4 bp-line_height-0 bp-col_100-active_700 bp-text_align-center">
                                                <img src="<?php echo esc_url( $overview_image_one_url ); ?>" class="bp-overview_img-1 bp-col_50-active_700 ">
                                                <img src="<?php echo esc_url( $overview_image_two_url ); ?>" class="bp-overview_img-2 bp-col_50-active_700 ">
                                            </div>
                                            <div class="bp-col bp-col_8 bp-line_height-0 bp-col_100-active_700">
                                                <img src="<?php echo esc_url( $overview_image_three_url ); ?>" class="bp-overview_img-3">
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="bp-overview_text-wrapper">
                                        <?php if( $overview_mission_enable ): ?>
                                            <div class="our-mission-section">
                                                <h3 class="bp-text_l bp-hanken_font">
                                                    <?php echo esc_html( $overview_mission_title ); ?>
                                                </h3>
                                                <?php if( ! empty( $overview_missions ) ): ?>
                                                    <ul class="bp-overview_list">
                                                        <?php foreach( $overview_missions as $overview_mission ): ?>
                                                            <li class="bp-flex bp-align_items-center bp-overview_list-item bp-text_l">
                                                                <span>
                                                                    <svg width="28" height="28" viewBox="0 0 28 28" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M20.668 2.45352C22.679 3.61466 24.3519 5.28088 25.5211 7.28725C26.6902 9.29362 27.3151 11.5705 27.3339 13.8926C27.3526 16.2147 26.7646 18.5015 25.628 20.5264C24.4914 22.5514 22.8456 24.2445 20.8536 25.4379C18.8616 26.6314 16.5925 27.2839 14.2708 27.3309C11.9491 27.3779 9.65537 26.8177 7.61671 25.7058C5.57805 24.5939 3.86513 22.9689 2.64752 20.9915C1.42992 19.0142 0.749848 16.7531 0.674636 14.4322L0.667969 14.0002L0.674636 13.5682C0.749306 11.2655 1.41937 9.02146 2.61949 7.05484C3.81961 5.08822 5.50884 3.46612 7.52249 2.34669C9.53614 1.22727 11.8055 0.648711 14.1093 0.667433C16.4131 0.686154 18.6728 1.30151 20.668 2.45352ZM18.944 10.3909C18.7144 10.1613 18.4089 10.0234 18.0849 10.003C17.7608 9.98262 17.4405 10.0812 17.184 10.2802L17.0586 10.3909L12.668 14.7802L10.944 13.0575L10.8186 12.9468C10.5621 12.748 10.2418 12.6495 9.91784 12.67C9.59389 12.6904 9.28851 12.8283 9.05898 13.0579C8.82946 13.2874 8.69154 13.5928 8.67111 13.9167C8.65067 14.2407 8.74911 14.561 8.94797 14.8175L9.05863 14.9428L11.7253 17.6095L11.8506 17.7202C12.0845 17.9016 12.372 18.0001 12.668 18.0001C12.9639 18.0001 13.2515 17.9016 13.4853 17.7202L13.6106 17.6095L18.944 12.2762L19.0546 12.1508C19.2536 11.8943 19.3522 11.574 19.3318 11.2499C19.3114 10.9259 19.1735 10.6204 18.944 10.3909Z" fill="#B872FF" />
                                                                    </svg>
                                                                </span>
                                                                <p class="bp-inter_font">
                                                                    <?php echo esc_html( $overview_mission['better_payment_campaign_overview_our_mission_item'] ); ?>
                                                                </p>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if( $overview_desc_two_enable ): ?>
                                            <p class="bp-text_secondary bp-inter_font bp-overview_text">
                                                <?php echo esc_html( $overview_desc_two ); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                                            
                            <?php if( $team_enable ): ?>
                                <div class="bp-section-footer bp-overview_team-wrapper">
                                    <?php if( ! empty( $team_title ) ): ?>
                                        <h3 class="bp-text_xxl bp-sora_font bp-tram_section-header">
                                            <?php echo esc_html( $team_title ); ?>
                                        </h3>
                                    <?php endif; ?>
                                    <?php if( ! empty( $team_members ) ): ?>
                                        <div class="bp-row bp-justify_content-center_active-700">
                                            <?php foreach( $team_members as $team_member ): ?>
                                                <?php if( ! empty( $team_member['better_payment_campaign_team_member_image']['url'] ) || ! empty( $team_member['better_payment_campaign_team_member_name'] ) ): ?>
                                                    <div class="bp-col_3 bp-col bp-col_50-active_700">
                                                        <div class="bp-team_wrapper">
                                                            <?php if( ! empty( $team_member['better_payment_campaign_team_member_image']['url'] ) ): ?>
                                                                <div class="img-box">
                                                                    <img src="<?php echo esc_url( $team_member['better_payment_campaign_team_member_image']['url'] ); ?>" alt="Team member" />
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if( ! empty( $team_member['better_payment_campaign_team_member_name'] ) ): ?>
                                                                <h4 class=" bp-inter_font member-name">
                                                                    <?php echo esc_html( $team_member['better_payment_campaign_team_member_name'] ); ?>
                                                                </h4>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    <div class="bp-tab_pane" id="comment">
                        <h3 class="bp-text_l bp-ibm_font bp-comment_header">Write a comment</h3>
                        <form class="bp-comment_form">
                            <textarea type="text" placeholder="Add Comment..."
                                class="bp-coomment_input bp-inter_font"></textarea>
                            <div class="bp-flex bp-justify_content-space_between">
                                <div>
                                    <button class="bp-text_tools" id="boldIcon">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M5.07377 0.666992C3.74508 0.666992 2.66797 1.7441 2.66797 3.07279V13.2944C2.66797 14.4207 3.58096 15.3337 4.70718 15.3337H9.33463C11.5438 15.3337 13.3346 13.5428 13.3346 11.3337C13.3346 9.61761 12.254 8.15395 10.7361 7.58606C11.5148 6.85623 12.0013 5.81843 12.0013 4.66699C12.0013 2.45785 10.2104 0.666992 8.0013 0.666992H5.07377ZM8.0013 7.33366C9.47406 7.33366 10.668 6.13975 10.668 4.66699C10.668 3.19423 9.47406 2.00033 8.0013 2.00033H5.07377C4.48146 2.00033 4.0013 2.48048 4.0013 3.07279V7.33366H8.0013ZM4.0013 8.66699V13.2944C4.0013 13.6843 4.31734 14.0003 4.70718 14.0003H9.33463C10.8074 14.0003 12.0013 12.8064 12.0013 11.3337C12.0013 9.8609 10.8074 8.66699 9.33463 8.66699H4.0013Z"
                                                fill="#1C274C" />
                                        </svg>
                                    </button>
                                    <button class="bp-text_tools" id="italicIcon">
                                        <svg width="14" height="16" viewBox="0 0 14 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M8.9832 0.666191H4.9987C4.63051 0.666191 4.33203 0.964668 4.33203 1.33286C4.33203 1.70105 4.63051 1.99952 4.9987 1.99952H8.10268L4.50268 13.9995H0.998698C0.630508 13.9995 0.332031 14.298 0.332031 14.6662C0.332031 15.0344 0.630508 15.3329 0.998698 15.3329H4.98372C4.9939 15.3331 5.00406 15.3331 5.0142 15.3329H8.9987C9.36689 15.3329 9.66536 15.0344 9.66536 14.6662C9.66536 14.298 9.36689 13.9995 8.9987 13.9995H5.89472L9.49472 1.99952H12.9987C13.3669 1.99952 13.6654 1.70105 13.6654 1.33286C13.6654 0.964668 13.3669 0.666191 12.9987 0.666191H9.01367C9.00349 0.665957 8.99333 0.665958 8.9832 0.666191Z"
                                                fill="#1C274C" />
                                        </svg>
                                    </button>
                                    <button class="bp-text_tools" id="underlineIcon">
                                        <svg width="12" height="14" viewBox="0 0 12 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M1.33333 0.999674C1.33333 0.631485 1.03486 0.333008 0.666667 0.333008C0.298477 0.333008 0 0.631485 0 0.999674V4.99967C0 8.31338 2.68629 10.9997 6 10.9997C9.31371 10.9997 12 8.31338 12 4.99967V0.999674C12 0.631485 11.7015 0.333008 11.3333 0.333008C10.9651 0.333008 10.6667 0.631485 10.6667 0.999674V4.99967C10.6667 7.577 8.57733 9.66634 6 9.66634C3.42267 9.66634 1.33333 7.577 1.33333 4.99967V0.999674Z"
                                                fill="#1C274C" />
                                            <path
                                                d="M0.666667 12.333C0.298477 12.333 0 12.6315 0 12.9997C0 13.3679 0.298477 13.6663 0.666667 13.6663H11.3333C11.7015 13.6663 12 13.3679 12 12.9997C12 12.6315 11.7015 12.333 11.3333 12.333H0.666667Z"
                                                fill="#1C274C" />
                                        </svg>
                                    </button>

                                    <button class="bp-text_tools" id="linkIcon">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M10.4847 2.58939C11.4409 1.62946 12.8396 1.6093 13.6139 2.38662C14.3897 3.1654 14.3688 4.57289 13.412 5.53347L11.7961 7.15564C11.6012 7.35128 11.6018 7.66786 11.7974 7.86274C11.9931 8.05763 12.3097 8.05701 12.5046 7.86137L14.1204 6.23921C15.3946 4.96007 15.5546 2.91785 14.3224 1.68088C13.0888 0.442449 11.0511 0.603857 9.77624 1.88366L6.54447 5.12796C5.27027 6.4071 5.1103 8.44934 6.34248 9.68631C6.53736 9.88195 6.85395 9.88256 7.04959 9.68768C7.24523 9.49279 7.24584 9.17621 7.05096 8.98057C6.27519 8.20179 6.29608 6.79427 7.25295 5.8337L10.4847 2.58939Z"
                                                fill="#1C274C" />
                                            <path
                                                d="M9.65575 6.31391C9.46087 6.11827 9.14429 6.11765 8.94865 6.31254C8.75301 6.50742 8.75239 6.824 8.94728 7.01964C9.72305 7.79843 9.70216 9.2059 8.74529 10.1665L5.51354 13.4108C4.55733 14.3708 3.15861 14.3909 2.38429 13.6136C1.60852 12.8348 1.62941 11.4273 2.58628 10.4667L4.20218 8.84454C4.39706 8.6489 4.39645 8.33232 4.20081 8.13743C4.00517 7.94255 3.68859 7.94316 3.4937 8.1388L1.87781 9.76097C0.603607 11.0401 0.443634 13.0824 1.67582 14.3193C2.90947 15.5578 4.94717 15.3964 6.22202 14.1166L9.45377 10.8722C10.728 9.59308 10.8879 7.55087 9.65575 6.31391Z"
                                                fill="#1C274C" />
                                        </svg>

                                    </button>
                                </div>

                                <button class="bp-comment_submit btn-1 bp-inter_font">Submit Now</button>
                            </div>

                        </form>

                        <div class="bp-comment_box">
                            <div class="bp-flex">
                                <h4 class="given-comment bp-ibm_font">Comment</h4>
                                <span class="bp-comment_count bp-ibm_font">02</span>
                            </div>

                            <ul class="bp-comment_list">
                                <li class="bp-comment_author bp-flex">
                                    <div class="author-img_box">
                                        <div class="author-img">
                                            <img src="<?php echo esc_url( BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/author.png' ); ?>" alt="author.png">
                                        </div>
                                    </div>
                                    <div class="author-info">
                                        <div class="bp-flex bp-align_items-center">
                                            <h5 class="author-name bp-manrope_font">Annette Black</h5>
                                            <div class="author-comment_date bp-manrope_font">Aug 19, 2024</div>
                                        </div>
                                        <p class="author-comment bp-ibm_font">
                                            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolores quae eum
                                            id officia? Nobis mollitia sequi tenetur eaque, corrupti dicta ab
                                            reprehenderit voluptates, neque quis assumenda autem commodi quasi dolorem!
                                        </p>
                                    </div>
                                </li>

                                <li class="bp-comment_author bp-flex">
                                    <div class="author-img_box">
                                        <div class="author-img">
                                            <img src="<?php echo esc_url( BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/author.png' ); ?>" alt="author.png">
                                        </div>
                                    </div>
                                    <div class="author-info">
                                        <div class="bp-flex bp-align_items-center">
                                            <h5 class="author-name bp-manrope_font">Annette Black</h5>
                                            <div class="author-comment_date bp-manrope_font">Aug 19, 2024</div>
                                        </div>
                                        <p class="author-comment bp-ibm_font">
                                            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolores quae eum
                                            id officia? Nobis mollitia sequi tenetur eaque, corrupti dicta ab
                                            reprehenderit voluptates, neque quis assumenda autem commodi quasi dolorem!
                                        </p>
                                    </div>
                                </li>

                            </ul>

                        </div>
                    </div>

                    <div class="bp-tab_pane" id="update">
                        <div class="bp-update_box">
                            <div class="bp-announcement_icon">
                                <img src="<?php echo esc_url( BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/announcement.png'); ?>" alt="announcement.png">
                            </div>
                            <h3 class="bp-announcement_header bp-hanken_font">
                                87% Raised: We’re Almost There
                            </h3>
                            <p class="bp-announcement bp-inter_font bp-text_secondary  ">
                                We’re 87% of the way to our goal of $23,000. Thanks to your generosity, we’ve raised
                                $20,010 to provide nutritious meals and hope to children in need. Your contributions are
                                breaking the cycle of hunger, one meal at a time.
                            </p>

                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</main>