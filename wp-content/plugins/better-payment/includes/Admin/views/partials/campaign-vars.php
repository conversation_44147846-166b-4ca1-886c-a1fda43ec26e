<?php

// Header related variables
$header_enable                  = ! empty( $settings['better_payment_campaign_general_header_enable'] ) && 'yes' === $settings['better_payment_campaign_general_header_enable'];
$header_title_enable            = ! empty( $settings['better_payment_campaign_general_title_enable'] ) && 'yes' === $settings['better_payment_campaign_general_title_enable'];
$header_short_desc_enable       = ! empty( $settings['better_payment_campaign_general_short_description_enable'] ) && 'yes' === $settings['better_payment_campaign_general_short_description_enable'];
$header_image_one_enable        = ! empty( $settings['better_payment_campaign_general_image_one_enable'] ) && 'yes' === $settings['better_payment_campaign_general_image_one_enable'];
$header_image_two_enable        = ! empty( $settings['better_payment_campaign_general_image_two_enable'] ) && 'yes' === $settings['better_payment_campaign_general_image_two_enable'];
$header_image_three_enable      = ! empty( $settings['better_payment_campaign_general_image_three_enable'] ) && 'yes' === $settings['better_payment_campaign_general_image_three_enable'];

$layout_header_enable           = false;
if( $settings['better_payment_campaign_layout'] === 'layout-1' ) {
    $layout_header_enable       = $header_enable && ( $header_title_enable || $header_short_desc_enable || $header_image_one_enable || $header_image_two_enable );
} else if( $settings['better_payment_campaign_layout'] === 'layout-2' ) {
    $layout_header_enable       = $header_enable && ( $header_title_enable || $header_short_desc_enable || $header_image_one_enable || $header_image_two_enable || $header_image_three_enable );
} else if( $settings['better_payment_campaign_layout'] === 'layout-3' ) {
    $layout_header_enable       = $header_enable && ( $header_title_enable || $header_short_desc_enable || $header_image_one_enable );
}

if ( $settings['better_payment_campaign_layout'] === 'layout-1' ) {
    $header_title               = ! empty( $settings['better_payment_campaign_header_title_text'] ) 
        ? $settings['better_payment_campaign_header_title_text'] 
        : __( 'Give Hope, Change Lives: Help Child in Need', 'better-payment' );
    $header_short_desc          = ! empty( $settings['better_payment_campaign_header_short_description'] ) 
        ? $settings['better_payment_campaign_header_short_description'] 
        : __( 'Every child deserves a life free from hunger. Your contribution can provide nutritious meals, hope and a brighter future for needy children.', 'better-payment' );
    $header_image_one_url       = ! empty( $settings['better_payment_campaign_header_image_one']['url'] ) 
        ? $settings['better_payment_campaign_header_image_one']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/group-1.svg';
    $header_image_two_url       = ! empty( $settings['better_payment_campaign_header_image_two']['url'] ) 
        ? $settings['better_payment_campaign_header_image_two']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/group-2.svg';
} else if ( $settings['better_payment_campaign_layout'] === 'layout-2' ) {
    $header_title               = ! empty( $settings['better_payment_campaign_header_title_text'] ) 
        ? $settings['better_payment_campaign_header_title_text'] 
        : __( 'Give Gaza\'s Children a Chance for Safety and Brighter Future', 'better-payment' );
    $header_short_desc          = ! empty( $settings['better_payment_campaign_header_short_description'] ) 
        ? $settings['better_payment_campaign_header_short_description'] 
        : __( 'Your generosity can transform lives. Your small donation token can provide food, shelter, medical aid and safety to children in urgent need.', 'better-payment' );
    $header_image_one_url       = ! empty( $settings['better_payment_campaign_header_image_one']['url'] ) 
        ? $settings['better_payment_campaign_header_image_one']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-2/bg-img-1.png';
    $header_image_two_url       = ! empty( $settings['better_payment_campaign_header_image_two']['url'] ) 
        ? $settings['better_payment_campaign_header_image_two']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-2/bg-img-2.png';
    $header_image_three_url     = ! empty( $settings['better_payment_campaign_header_image_three']['url'] ) 
        ? $settings['better_payment_campaign_header_image_three']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-2/bg-img-3.png';
} else if ( $settings['better_payment_campaign_layout'] === 'layout-3' ) {
    $header_title               = ! empty( $settings['better_payment_campaign_header_title_text'] ) 
        ? $settings['better_payment_campaign_header_title_text'] 
        : __( 'Building Bridges to Change Lives', 'better-payment' );
    $header_short_desc          = ! empty( $settings['better_payment_campaign_header_short_description'] ) 
        ? $settings['better_payment_campaign_header_short_description'] 
        : __( 'We believe every animal deserves a happy and healthy life. By contributing to our fundraiser, you\'re supporting rescue operations, medical treatments, and rehoming efforts for animals in desperate need.', 'better-payment' );
    $header_image_one_url       = ! empty( $settings['better_payment_campaign_header_image_one']['url'] ) 
        ? $settings['better_payment_campaign_header_image_one']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-3/hero.png';
}

// Form related variables
$button_text                = ! empty( $settings['better_payment_campaign_form_button_text'] ) 
    ? $settings['better_payment_campaign_form_button_text'] 
    : __( 'Donate Now', 'better-payment' );
$button_link                = ! empty( $settings['better_payment_campaign_form_button_link']['url'] ) 
    ? $settings['better_payment_campaign_form_button_link']['url'] 
    : 'javascript:void(0)';
if( $settings['better_payment_campaign_layout'] === 'layout-1' ) {
    $form_title_text            = $settings['better_payment_campaign_form_title_text_layout_1'];
    $form_image                 = $settings['better_payment_campaign_form_image']['url'];
    $form_goal_label            = $settings['better_payment_campaign_form_goal_amount_label'];
    $form_goal_amount           = $settings['better_payment_campaign_form_goal_amount'];
    $form_goal_percentage_enable= ! empty( $settings['better_payment_campaign_form_goal_percentage_enable'] ) && 'yes' === $settings['better_payment_campaign_form_goal_percentage_enable'];
    $form_goal_bar_line_enable  = ! empty( $settings['better_payment_campaign_form_goal_bar_line_enable'] ) && 'yes' === $settings['better_payment_campaign_form_goal_bar_line_enable'];
    $placeholder_text           = $settings['better_payment_campaign_form_placeholder_text_layout_1'];
    $amount_items               = ! empty( $settings['better_payment_campaign_form_amount_list_layout_1'] ) 
        ? $settings['better_payment_campaign_form_amount_list_layout_1'] 
        : [];
} else if( $settings['better_payment_campaign_layout'] === 'layout-2' ) {
    $form_title_text            = $settings['better_payment_campaign_form_title_text_layout_2'];
    $form_sub_title_text        = $settings['better_payment_campaign_form_sub_title_text'];
    $form_goal_amount           = $settings['better_payment_campaign_form_goal_amount'];
    $form_goal_amount_raised_enable = ! empty( $settings['better_payment_campaign_form_goal_amount_raised_enable'] ) && 'yes' === $settings['better_payment_campaign_form_goal_amount_raised_enable'];
    $form_goal_amount_raised_label = $settings['better_payment_campaign_form_goal_amount_raised_label'];
    $form_goal_bar_line_enable  = ! empty( $settings['better_payment_campaign_form_goal_bar_line_enable'] ) && 'yes' === $settings['better_payment_campaign_form_goal_bar_line_enable'];
    $form_total_donation_enable = ! empty( $settings['better_payment_campaign_form_total_donation_enable'] ) && 'yes' === $settings['better_payment_campaign_form_total_donation_enable'];
    $form_total_donation_label  = $settings['better_payment_campaign_form_total_donation_label'];
} else if( $settings['better_payment_campaign_layout'] === 'layout-3' ) {
    $placeholder_text           = $settings['better_payment_campaign_form_placeholder_text_layout_3'];
    $amount_items               = ! empty( $settings['better_payment_campaign_form_amount_list_layout_3'] ) 
        ? $settings['better_payment_campaign_form_amount_list_layout_3'] 
        : [];
}

// Overview related variables
$overview_enable                = ! empty( $settings['better_payment_campaign_general_overview_enable'] ) && 'yes' === $settings['better_payment_campaign_general_overview_enable'];
$overview_images_enable         = ! empty( $settings['better_payment_campaign_general_overview_images_enable'] ) && 'yes' === $settings['better_payment_campaign_general_overview_images_enable'];
$overview_desc_one_enable       = ! empty( $settings['better_payment_campaign_general_overview_description_one_enable'] ) && 'yes' === $settings['better_payment_campaign_general_overview_description_one_enable'];
$overview_desc_two_enable       = ! empty( $settings['better_payment_campaign_general_overview_description_two_enable'] ) && 'yes' === $settings['better_payment_campaign_general_overview_description_two_enable'];
$overview_mission_enable        = ! empty( $settings['better_payment_campaign_general_overview_mossion_enable'] ) && 'yes' === $settings['better_payment_campaign_general_overview_mossion_enable'];

$layout_overview_enable           = false;
if( $settings['better_payment_campaign_layout'] === 'layout-1' ) {
    $layout_overview_enable       = $overview_enable && ( $overview_images_enable || $overview_desc_one_enable || $overview_desc_two_enable || $overview_mission_enable );
} else if( $settings['better_payment_campaign_layout'] === 'layout-2' ) {
    $layout_overview_enable       = $overview_enable && $overview_desc_one_enable;
} else if( $settings['better_payment_campaign_layout'] === 'layout-3' ) {
    $layout_overview_enable       = $overview_enable && ( $overview_images_enable || $overview_desc_one_enable || $overview_desc_two_enable );
}

if ( $settings['better_payment_campaign_layout'] === 'layout-1' ) {
    $overview_title               = ! empty( $settings['better_payment_campaign_overview_title_text'] ) 
        ? $settings['better_payment_campaign_overview_title_text'] 
        : __( 'Overview', 'better-payment' );
    $overview_desc_one            = ! empty( $settings['better_payment_campaign_overview_description_one'] ) 
        ? $settings['better_payment_campaign_overview_description_one'] 
        : __( 'We believe every animal deserves a happy and healthy life. By contributing to our fundraiser, you\'re supporting rescue operations, medical treatments, and rehoming efforts for animals in desperate need. and rehoming efforts for animals in desperate need.', 'better-payment' );
    $overview_desc_two            = ! empty( $settings['better_payment_campaign_overview_description_two'] ) 
        ? $settings['better_payment_campaign_overview_description_two'] 
        : __( 'Your generosity fuels our vision of a world where every child has the chance to grow, learn, and thrive without the shadow of hunger.', 'better-payment' );
    $overview_image_one_url       = ! empty( $settings['better_payment_campaign_overview_image_one']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_one']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/image-260.png';
    $overview_image_two_url       = ! empty( $settings['better_payment_campaign_overview_image_two']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_two']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/image-263.png';
    $overview_image_three_url     = ! empty( $settings['better_payment_campaign_overview_image_three']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_three']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/image-262.png';
    $overview_mission_title       = ! empty( $settings['better_payment_campaign_overview_our_mission_title_text'] ) 
        ? $settings['better_payment_campaign_overview_our_mission_title_text'] 
        : __( 'Our Mission', 'better-payment' );
    $overview_missions            = ! empty( $settings['better_payment_campaign_overview_our_mission_items'] ) 
        ? $settings['better_payment_campaign_overview_our_mission_items'] 
        : [
            [
                'better_payment_campaign_overview_our_mission_item' => 'Provide nutritious meals to children in need',
            ],
            [
                'better_payment_campaign_overview_our_mission_item' => 'Offer educational support and resources',
            ],
            [
                'better_payment_campaign_overview_our_mission_item' => 'Create safe and nurturing environments',
            ],
            [
                'better_payment_campaign_overview_our_mission_item' => 'Promote awareness and advocacy for child rights',
            ],
        ];
} else if ( $settings['better_payment_campaign_layout'] === 'layout-2' ) {
    $overview_title               = ! empty( $settings['better_payment_campaign_overview_title_text'] ) 
        ? $settings['better_payment_campaign_overview_title_text'] 
        : __( 'Overview', 'better-payment' );
    $overview_desc_one            = ! empty( $settings['better_payment_campaign_overview_description_one'] ) 
        ? $settings['better_payment_campaign_overview_description_one'] 
        : __( 'Children in Gaza are facing unimaginable hardships, but your support can provide a lifeline. Every donation, big or small, helps provide essentials like food, shelter, and medical care. Your generosity can transform lives, offering hope and safety to those in need.', 'better-payment' );
} else if ( $settings['better_payment_campaign_layout'] === 'layout-3' ) {
    $overview_title               = ! empty( $settings['better_payment_campaign_overview_title_text'] ) 
        ? $settings['better_payment_campaign_overview_title_text'] 
        : __( 'Lending Hands, Healing Hearts', 'better-payment' );
    $overview_desc_one            = ! empty( $settings['better_payment_campaign_overview_description_one'] ) 
        ? $settings['better_payment_campaign_overview_description_one'] 
        : __( 'Every individual deserves hope, compassion and a chance at a better tomorrow. Your support helps provide essential resources such as food, clothing and shelter to those whose are in need. Together, we can create opportunities for education, skill-building and empowerment.
        <br /> <br />
        Extend a spiritual and humanitarian helping hand to those in desperate need and ensure no one is left behind. Join us in building a world where every soul can thrive. Your kindness can make all the difference. Through your generosity, we can fund critical programs to help underprivileged and senior citizens.', 'better-payment' );
    $overview_desc_two            = ! empty( $settings['better_payment_campaign_overview_description_two'] ) 
        ? $settings['better_payment_campaign_overview_description_two'] 
        : __( 'From providing warm meals and safe places to stay, to educational initiatives and healthcare services, every humanitarian contribution brings us closer to ending the cycle of poverty and inequality. By empowering underprivileged and senior citizens, we uplift entire communities around the world.', 'better-payment' );
    $overview_image_one_url       = ! empty( $settings['better_payment_campaign_overview_image_one']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_one']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-3/img-1.png';
    $overview_image_two_url       = ! empty( $settings['better_payment_campaign_overview_image_two']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_two']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-3/img-2.png';
    $overview_image_three_url     = ! empty( $settings['better_payment_campaign_overview_image_three']['url'] ) 
        ? $settings['better_payment_campaign_overview_image_three']['url'] 
        : BETTER_PAYMENT_ASSETS . '/img/campaign/layout-3/img-3.png';
}


// Team related variables
$team_enable = ! empty( $settings['better_payment_campaign_general_footer_team_enable_layout_1'] ) && 'yes' === $settings['better_payment_campaign_general_footer_team_enable_layout_1'];
$team_title = ! empty( $settings['better_payment_campaign_footer_our_team_title_text'] ) 
    ? $settings['better_payment_campaign_footer_our_team_title_text'] 
    : '';
$team_members = ! empty( $settings['better_payment_campaign_team_members'] ) 
    ? $settings['better_payment_campaign_team_members'] 
    : [
        [
            'better_payment_campaign_team_member_name' => 'Annette Black',
            'better_payment_campaign_team_member_image' => [
                'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-1.png',
            ],
        ],
        [
            'better_payment_campaign_team_member_name' => 'Darrell Steward',
            'better_payment_campaign_team_member_image' => [
                'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-2.png',
            ],
        ],
        [
            'better_payment_campaign_team_member_name' => 'Theresa Webb',
            'better_payment_campaign_team_member_image' => [
                'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-3.png',
            ],
        ],
        [
            'better_payment_campaign_team_member_name' => 'Guy Hawkins',
            'better_payment_campaign_team_member_image' => [
                'url' => BETTER_PAYMENT_ASSETS . '/img/campaign/layout-1/team-4.png',
            ],
        ],
    ];

// Related Campaigns related variables
$related_campaign_enable = ! empty( $settings['better_payment_campaign_general_footer_related_campaign_enable_layout_2'] ) && 'yes' === $settings['better_payment_campaign_general_footer_related_campaign_enable_layout_2'];
$related_campaigns = ! empty( $settings['better_payment_campaign_related_campaigns'] ) 
    ? $settings['better_payment_campaign_related_campaigns'] 
    : [
        [
            'better_payment_campaign_related_campaign_id' => '',
        ],
        [
            'better_payment_campaign_related_campaign_id' => '',
        ],
        [
            'better_payment_campaign_related_campaign_id' => '',
        ],
        [
            'better_payment_campaign_related_campaign_id' => '',
        ],
    ];