(()=>{var a;(a=jQuery)(document).ready((function(){a(document).on("click",".bp-tab_button",(function(){a(".bp-tab_button").removeClass("bp-active"),a(this).addClass("bp-active");var e=a(this).data("tab");a(".bp-tab_pane").hide(),a("#"+e).show()})),a(document).on("click",".bp-learn_more-btn",(function(e){e.preventDefault(),function(){const e=a(".bp-announcement"),t=a(".more-btn_text");e.hasClass("active")?(e.removeClass("active"),t.text("Learn More")):(e.addClass("active"),t.text("Show Less"))}()})),a(document).on("click",'.bp-payment_item input[name="option_amount"]',(function(){var e=a(this).val();a(".other_amount").val(e),a('label[for^="amount_"]').removeClass("active_border"),a('label[for="'+a(this).attr("id")+'"]').addClass("active_border")})),a(document).on("focus",".bp-payment_form .other_amount",(function(){a('input[name="option_amount"]').prop("checked",!1),a('label[for^="amount_"]').removeClass("active_border")})),a(document).on("change",'.bp-donate_amounts input[type="radio"]',(function(){a(".bp-donate_amounts .bp-radio_label").removeClass("bp-radio_active"),a(this).closest(".bp-radio_label").addClass("bp-radio_active");var e=a(this).data("value");a(".bp-physical_add-amount").val(e)})),a(document).on("focus",".bp-physical_add-amount",(function(){a('input[type="radio"]').prop("checked",!1),a(".bp-radio_label").removeClass("bp-radio_active")})),function(){const e=a(".img-wrapper img"),t=a("#bgImage"),o=a(".img-wrapper"),n=a(".image_over-lay"),i=e.map((function(){return a(this).data("bg")})).get();let c,s=0;const r=n=>{i[n]&&(t.css("background-image",`url('${i[n]}')`),o.each((function(e){const t=a(this),o=t.find(".image_over-lay");e===n?(o.hide(),t.css("border","2px solid #FBBF24")):(o.show(),t.css("border","none"))})),e.removeClass("active").eq(n).addClass("active"))},d=()=>{c=setInterval((()=>{s=(s+1)%i.length,r(s)}),5e3)};n.each((function(e){a(this).on("click",(function(){clearInterval(c),s=e,r(e),d()}))})),r(s),d()}()}))})();