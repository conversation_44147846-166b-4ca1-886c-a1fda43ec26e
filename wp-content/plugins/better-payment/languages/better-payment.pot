# Copyright (C) 2025 Better Payment
# This file is distributed under the same license as the Better Payment package.
msgid ""
msgstr ""
"Project-Id-Version: Better Payment\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../includes/Admin.php:80, ../includes/Admin/Settings.php:90, ../includes/Admin/Settings.php:91, ../includes/Admin/views/better-payment-settings.php:64
msgid "Settings"
msgstr ""

#: ../includes/Admin.php:83
msgid "Go Pro"
msgstr ""

#: ../includes/Assets.php:165, ../includes/Assets.php:182, ../includes/Admin/Setup_Wizard.php:73
msgid "Are you sure?"
msgstr ""

#: ../includes/Assets.php:166, ../includes/Assets.php:175
msgid "Something went wrong"
msgstr ""

#: ../includes/Assets.php:168
msgid "Redirecting"
msgstr ""

#: ../includes/Assets.php:171
msgid "field is required"
msgstr ""

#: ../includes/Assets.php:172
msgid "Business Email is required"
msgstr ""

#: ../includes/Assets.php:173
msgid "Payment Amount field is required"
msgstr ""

#: ../includes/Assets.php:174
msgid "Minimum amount is 1"
msgstr ""

#: ../includes/Assets.php:183, ../includes/Admin/Setup_Wizard.php:74
msgid "You won't be able to revert this!"
msgstr ""

#: ../includes/Assets.php:184, ../includes/Admin/Setup_Wizard.php:75
msgid "Yes, delete it!"
msgstr ""

#: ../includes/Assets.php:185, ../includes/Admin/Setup_Wizard.php:76
msgid "No, cancel!"
msgstr ""

#: ../includes/Assets.php:188, ../includes/Admin/Setup_Wizard.php:79
msgid "Changes saved successfully!"
msgstr ""

#: ../includes/Assets.php:189, ../includes/Admin/Setup_Wizard.php:80
msgid "Opps! something went wrong!"
msgstr ""

#: ../includes/Assets.php:190, ../includes/Admin/Setup_Wizard.php:81
msgid "No action taken!"
msgstr ""

#: ../includes/Admin/Settings.php:79, ../includes/Admin/Settings.php:80, ../includes/Classes/Actions.php:104, ../includes/Classes/Actions.php:247, ../includes/Classes/Actions.php:253, ../includes/Admin/Elementor/Better_Payment_Widget.php:59, ../includes/Admin/Elementor/Better_Payment_Widget.php:870, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:34, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:46, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:107
msgid "Better Payment"
msgstr ""

#: ../includes/Admin/Settings.php:102, ../includes/Admin/Elementor/User_Dashboard.php:169, ../includes/Admin/Elementor/User_Dashboard.php:239, ../includes/Admin/Elementor/User_Dashboard.php:242
msgid "Transactions"
msgstr ""

#: ../includes/Admin/Settings.php:110
msgid "Analytics"
msgstr ""

#: ../includes/Admin/Settings.php:206, ../includes/Admin/Settings.php:343
msgid "Record not found!"
msgstr ""

#: ../includes/Admin/Settings.php:329, ../includes/Admin/Settings.php:334, ../includes/Admin/Settings.php:362, ../includes/Admin/Settings.php:367, ../includes/Admin/Settings.php:403, ../includes/Admin/Settings.php:408, ../includes/Admin/Settings.php:671, ../includes/Admin/Settings.php:676, ../includes/Classes/Export.php:38, ../includes/Classes/Export.php:43
msgid "Access Denied!"
msgstr ""

#: ../includes/Admin/Settings.php:388
msgid "Something went wrong!"
msgstr ""

#: ../includes/Admin/Settings.php:374
msgid "Deleted Successfully!"
msgstr ""

#: ../includes/Admin/Settings.php:689
msgid "Failed to mark transaction as completed!"
msgstr ""

#: ../includes/Admin/Settings.php:686
msgid "Transaction marked as completed!"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:102, ../includes/Admin/Setup_Wizard.php:103
msgid "Better Payment "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:135
msgid "Getting Started"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:147
msgid "PayPal Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:159
msgid "Stripe Configuration"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:169
msgid "Finalize"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:226, ../includes/Admin/views/better-payment-settings.php:98, ../includes/Admin/views/better-payment-settings.php:109, ../includes/Admin/views/better-payment-transaction-list.php:276, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:32, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:44, ../includes/Admin/views/elementor/layouts/layout-1.php:70, ../includes/Admin/views/elementor/layouts/layout-2.php:38
msgid "PayPal"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:227, ../includes/Admin/views/better-payment-settings.php:111
msgid "Enable PayPal if you want to make transaction using PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:239, ../includes/Admin/views/better-payment-settings.php:99, ../includes/Admin/views/better-payment-settings.php:125, ../includes/Admin/views/better-payment-transaction-list.php:277, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:29, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:45, ../includes/Admin/views/elementor/layouts/layout-1.php:84, ../includes/Admin/views/elementor/layouts/layout-2.php:52
msgid "Stripe"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:240, ../includes/Admin/views/better-payment-settings.php:127
msgid "Enable Stripe if you want to accept payment via Stripe."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:252, ../includes/Admin/views/better-payment-settings.php:174
msgid "Email Notification"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:253, ../includes/Admin/views/better-payment-settings.php:175
msgid "Enable email notification for each transaction. It sends notification to the website admin and customer (who makes the payment). You can modify email settings as per your need."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:265, ../includes/Classes/Handler.php:589, ../includes/Admin/Elementor/Better_Payment_Widget.php:624, ../includes/Admin/Elementor/Better_Payment_Widget.php:2254, ../includes/Admin/Elementor/Better_Payment_Widget.php:2256, ../includes/Admin/Elementor/Better_Payment_Widget.php:2257, ../includes/Admin/views/better-payment-settings.php:187, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:57, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:58
msgid "Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:266, ../includes/Admin/views/better-payment-settings.php:188
msgid "Select default currency for each transaction. You can also overwrite this setting from each widget control on elementor page builder."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:271, ../includes/Admin/views/better-payment-settings.php:193
msgid "Select Currency"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:287, ../includes/Admin/Setup_Wizard.php:314, ../includes/Admin/Elementor/Better_Payment_Widget.php:1609, ../includes/Admin/Elementor/Better_Payment_Widget.php:1713, ../includes/Admin/Elementor/Better_Payment_Widget.php:1779, ../includes/Admin/views/better-payment-settings.php:361, ../includes/Admin/views/better-payment-settings.php:440, ../includes/Admin/views/better-payment-settings.php:509, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:115, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:107, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:121
msgid "Live Mode"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:288, ../includes/Admin/views/better-payment-settings.php:362
msgid "Live mode allows you to process real transactions. It just requires PayPal business email to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:300, ../includes/Admin/Elementor/Better_Payment_Widget.php:1579, ../includes/Admin/views/better-payment-settings.php:374, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:85
msgid "Business Email"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:301, ../includes/Admin/views/better-payment-settings.php:375
msgid "Your PayPal account email address to accept payment via PayPal."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:315, ../includes/Admin/views/better-payment-settings.php:441
msgid "Live mode allows you to process real transactions. It just requires live Stripe keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:330, ../includes/Admin/Elementor/Better_Payment_Widget.php:1674, ../includes/Admin/views/better-payment-settings.php:456, ../includes/Admin/views/better-payment-settings.php:525
msgid "Live Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:331, ../includes/Admin/views/better-payment-settings.php:458
msgid "Stripe live public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:339, ../includes/Admin/Elementor/Better_Payment_Widget.php:1693, ../includes/Admin/views/better-payment-settings.php:468, ../includes/Admin/views/better-payment-settings.php:537
msgid "Live Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:340, ../includes/Admin/views/better-payment-settings.php:470
msgid "Stripe live secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:348, ../includes/Admin/Elementor/Better_Payment_Widget.php:1635, ../includes/Admin/views/better-payment-settings.php:480, ../includes/Admin/views/better-payment-settings.php:549
msgid "Test Public Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:349, ../includes/Admin/views/better-payment-settings.php:482
msgid "Stripe test public key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:357, ../includes/Admin/Elementor/Better_Payment_Widget.php:1654, ../includes/Admin/views/better-payment-settings.php:492, ../includes/Admin/views/better-payment-settings.php:561
msgid "Test Secret Key"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:358, ../includes/Admin/views/better-payment-settings.php:494
msgid "Stripe test secret key is required to make payments via Stripe. For more help visit"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:379
msgid "Great Job! Your Configuration is Complete "
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:381
msgid "Share non-sensitive diagnostic data and plugin usage information."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:383
msgid "What do we collect? We collect non-sensitive diagnostic data and plugin usage information. Your site URL, WordPress & PHP version, plugins & themes and email address to send you the discount coupon. This data lets us make sure this plugin always stays compatible with the most popular plugins and themes. No spam, we promise."
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:399
msgid "< Previous"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:400
msgid "Next >"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:401
msgid "Finish"
msgstr ""

#: ../includes/Admin/Setup_Wizard.php:517
msgid "Quick Setup Wizard - Better Payment"
msgstr ""

#: ../includes/Classes/Actions.php:166, ../includes/Classes/Actions.php:388
msgid "Page ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:173, ../includes/Classes/Actions.php:392
msgid "Widget ID is missing"
msgstr ""

#: ../includes/Classes/Actions.php:186, ../includes/Classes/Actions.php:405
msgid "Setting Data is missing"
msgstr ""

#: ../includes/Classes/Actions.php:206
msgid "Stripe Key missing"
msgstr ""

#: ../includes/Classes/Actions.php:409
msgid "Paystack Key missing"
msgstr ""

#: ../includes/Classes/Handler.php:228, ../includes/Classes/Handler.php:380, ../includes/Classes/Handler.php:469
msgid "USD"
msgstr ""

#: ../includes/Classes/Handler.php:264
msgid "Payment under processing!"
msgstr ""

#: ../includes/Classes/Handler.php:319, ../includes/Classes/Handler.php:340
msgid "There was a problem connecting to the Stripe API endpoint."
msgstr ""

#: ../includes/Classes/Handler.php:547, ../includes/Admin/Elementor/Better_Payment_Widget.php:2185, ../includes/Admin/Elementor/Better_Payment_Widget.php:2186, ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "You paid"
msgstr ""

#: ../includes/Classes/Handler.php:547
msgid " to "
msgstr ""

#: ../includes/Classes/Handler.php:557
msgid "Payment Confirmation email will be sent to "
msgstr ""

#: ../includes/Classes/Handler.php:580, ../includes/Admin/views/template-email-notification.php:173
msgid "Thank You!"
msgstr ""

#: ../includes/Classes/Handler.php:583, ../includes/Admin/Elementor/Better_Payment_Widget.php:2205, ../includes/Admin/Elementor/Better_Payment_Widget.php:2206, ../includes/Admin/Elementor/User_Dashboard.php:1907, ../includes/Admin/Elementor/User_Dashboard.php:2030, ../includes/Admin/Elementor/User_Dashboard.php:2033, ../includes/Admin/views/template-transaction-list.php:44
msgid "Transaction ID"
msgstr ""

#: ../includes/Classes/Handler.php:586, ../includes/Admin/Elementor/Better_Payment_Widget.php:1209, ../includes/Admin/Elementor/Better_Payment_Widget.php:2237, ../includes/Admin/Elementor/Better_Payment_Widget.php:2239, ../includes/Admin/Elementor/Better_Payment_Widget.php:2240, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:591, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:633, ../includes/Admin/Elementor/User_Dashboard.php:1883, ../includes/Admin/Elementor/User_Dashboard.php:2004, ../includes/Admin/Elementor/User_Dashboard.php:2007, ../includes/Admin/views/better-payment-transaction-list.php:229, ../includes/Admin/views/template-email-notification.php:328, ../includes/Admin/views/template-transaction-list.php:38
msgid "Amount"
msgstr ""

#: ../includes/Classes/Handler.php:590, ../includes/Admin/Elementor/Better_Payment_Widget.php:2271, ../includes/Admin/Elementor/Better_Payment_Widget.php:2273, ../includes/Admin/Elementor/Better_Payment_Widget.php:2274, ../includes/Admin/Elementor/Better_Payment_Widget.php:3267, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2013
msgid "Payment Method"
msgstr ""

#: ../includes/Classes/Handler.php:593, ../includes/Admin/Elementor/Better_Payment_Widget.php:116, ../includes/Admin/Elementor/Better_Payment_Widget.php:2288, ../includes/Admin/Elementor/Better_Payment_Widget.php:2290, ../includes/Admin/Elementor/Better_Payment_Widget.php:2291, ../includes/Admin/Elementor/User_Dashboard.php:1895, ../includes/Admin/Elementor/User_Dashboard.php:2017, ../includes/Admin/Elementor/User_Dashboard.php:2020, ../includes/Admin/views/template-transaction-list.php:41
msgid "Payment Type"
msgstr ""

#: ../includes/Classes/Handler.php:603
msgid "Split Payment"
msgstr ""

#: ../includes/Classes/Handler.php:601
msgid "Recurring Payment"
msgstr ""

#: ../includes/Classes/Handler.php:599
msgid "One Time Payment"
msgstr ""

#: ../includes/Classes/Handler.php:608, ../includes/Admin/Elementor/Better_Payment_Widget.php:2305, ../includes/Admin/Elementor/Better_Payment_Widget.php:2307, ../includes/Admin/Elementor/Better_Payment_Widget.php:2308
msgid "Merchant Details"
msgstr ""

#: ../includes/Classes/Handler.php:614, ../includes/Admin/Elementor/Better_Payment_Widget.php:2322, ../includes/Admin/Elementor/Better_Payment_Widget.php:2324, ../includes/Admin/Elementor/Better_Payment_Widget.php:2325
msgid "Paid Amount"
msgstr ""

#: ../includes/Classes/Handler.php:617, ../includes/Admin/Elementor/Better_Payment_Widget.php:2339, ../includes/Admin/Elementor/Better_Payment_Widget.php:2341, ../includes/Admin/Elementor/Better_Payment_Widget.php:2342
msgid "Purchase Details"
msgstr ""

#: ../includes/Classes/Handler.php:620, ../includes/Admin/Elementor/Better_Payment_Widget.php:2356, ../includes/Admin/Elementor/Better_Payment_Widget.php:2358, ../includes/Admin/Elementor/Better_Payment_Widget.php:2359
msgid "Print"
msgstr ""

#: ../includes/Classes/Handler.php:623, ../includes/Admin/Elementor/Better_Payment_Widget.php:2373, ../includes/Admin/Elementor/Better_Payment_Widget.php:2375, ../includes/Admin/Elementor/Better_Payment_Widget.php:2376
msgid "View Details"
msgstr ""

#: ../includes/Classes/Handler.php:805, ../includes/Admin/Elementor/Better_Payment_Widget.php:2441
msgid "Payment Failed"
msgstr ""

#: ../includes/Classes/Handler.php:849, ../includes/Admin/Elementor/Better_Payment_Widget.php:1834, ../includes/Admin/Elementor/Better_Payment_Widget.php:2001
msgid "Better Payment transaction on %s"
msgstr ""

#: ../includes/Classes/Handler.php:989, ../includes/Classes/Handler.php:993
msgid "New better payment transaction! "
msgstr ""

#: ../includes/Classes/Handler.php:1122
msgid "Field"
msgstr ""

#: ../includes/Classes/Handler.php:1123
msgid "Entry"
msgstr ""

#: ../includes/Classes/Handler.php:1176, ../includes/Admin/views/template-email-notification.php:252
msgid "Paid"
msgstr ""

#: ../includes/Classes/Handler.php:1179
msgid "Product Price: "
msgstr ""

#: ../includes/Classes/Helper.php:51
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be installed and activated. Please install Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:52
msgid "Install Elementor"
msgstr ""

#: ../includes/Classes/Helper.php:45
msgid "%1$sBetter Payment%2$s requires %1$sElementor%2$s plugin to be active. Please activate Elementor to continue."
msgstr ""

#: ../includes/Classes/Helper.php:47
msgid "Activate Elementor"
msgstr ""

#: ../includes/Classes/Import.php:38, ../includes/Classes/Import.php:137
msgid "Invalid File!"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:416
msgid "We can't detect any plugin information. This is most probably because you have not included the code in the plugin main file."
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:778
msgid "Sorry to see you go"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:779
msgid "Before you deactivate the plugin, would you quickly give us your reason for doing so?"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:782
msgid "I no longer need the plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:784
msgid "I found a better plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:785
msgid "Please share which plugin"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:787
msgid "I couldn't get the plugin to work"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:788
msgid "It's a temporary deactivation"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:790
msgid "Other"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:791
msgid "Please share the reason"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:834
msgid "Submitting form"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Submit and Deactivate"
msgstr ""

#: ../includes/Classes/Plugin_Usage_Tracker.php:892
msgid "Just Deactivate"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:97
msgid "Payment Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:106
msgid "Form Layout"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:117
msgid "Recurring and Split Payment is available for Stripe only at the moment!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:134
msgid "Default Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:137
msgid "<p>Create a product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the (default) price id.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:158
msgid "Installment Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:171, ../includes/Admin/Elementor/Better_Payment_Widget.php:265
msgid "Price ID"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:184
msgid "Iterations"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:196
msgid "<p>Now add more prices to the product from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the price id for each installment.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:213
msgid "Installments"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:252
msgid "Interval Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:278
msgid "Intervals"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:303
msgid "Webhook Secret"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:306
msgid "<p>Create a webhook endpoint from Stripe dashboard and <a href=\"%1$s\" target=\"_blank\">get the webhook secret.</a></p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:326
msgid "<p><a href=\"%1$s\" target=\"_blank\">Your webhook endpoint url »</a><br>%2$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:344
msgid "Payment Source"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:359
msgid "<a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> is not installed/activated on your site. Please install and activate <a href=\"%1$s\" target=\"_blank\"><strong>WooCommerce</strong></a> first."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:394
msgid "Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:403
msgid "Choose a Product"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:404
msgid "Enter Product IDs separated by a comma"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:410
msgid "Search By"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:419
msgid "Select Products"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:433
msgid "Enable PayPal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:435, ../includes/Admin/Elementor/Better_Payment_Widget.php:461, ../includes/Admin/Elementor/Better_Payment_Widget.php:538, ../includes/Admin/Elementor/Better_Payment_Widget.php:581, ../includes/Admin/Elementor/Better_Payment_Widget.php:593, ../includes/Admin/Elementor/Better_Payment_Widget.php:613, ../includes/Admin/Elementor/Better_Payment_Widget.php:965, ../includes/Admin/Elementor/Better_Payment_Widget.php:992, ../includes/Admin/Elementor/Better_Payment_Widget.php:1041, ../includes/Admin/Elementor/Better_Payment_Widget.php:1074, ../includes/Admin/Elementor/Better_Payment_Widget.php:1088, ../includes/Admin/Elementor/Better_Payment_Widget.php:1326, ../includes/Admin/Elementor/Better_Payment_Widget.php:1342, ../includes/Admin/Elementor/Better_Payment_Widget.php:1611, ../includes/Admin/Elementor/Better_Payment_Widget.php:1715, ../includes/Admin/Elementor/Better_Payment_Widget.php:1781, ../includes/Admin/Elementor/Better_Payment_Widget.php:1861, ../includes/Admin/Elementor/Better_Payment_Widget.php:1873, ../includes/Admin/Elementor/Better_Payment_Widget.php:1885, ../includes/Admin/Elementor/Better_Payment_Widget.php:1897, ../includes/Admin/Elementor/Better_Payment_Widget.php:1909, ../includes/Admin/Elementor/Better_Payment_Widget.php:1921, ../includes/Admin/Elementor/Better_Payment_Widget.php:2029, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:80, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:119, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:176, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:283, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:117, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:109, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:123
msgid "Yes"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:436, ../includes/Admin/Elementor/Better_Payment_Widget.php:462, ../includes/Admin/Elementor/Better_Payment_Widget.php:539, ../includes/Admin/Elementor/Better_Payment_Widget.php:582, ../includes/Admin/Elementor/Better_Payment_Widget.php:594, ../includes/Admin/Elementor/Better_Payment_Widget.php:614, ../includes/Admin/Elementor/Better_Payment_Widget.php:966, ../includes/Admin/Elementor/Better_Payment_Widget.php:993, ../includes/Admin/Elementor/Better_Payment_Widget.php:1042, ../includes/Admin/Elementor/Better_Payment_Widget.php:1075, ../includes/Admin/Elementor/Better_Payment_Widget.php:1089, ../includes/Admin/Elementor/Better_Payment_Widget.php:1327, ../includes/Admin/Elementor/Better_Payment_Widget.php:1343, ../includes/Admin/Elementor/Better_Payment_Widget.php:1612, ../includes/Admin/Elementor/Better_Payment_Widget.php:1716, ../includes/Admin/Elementor/Better_Payment_Widget.php:1782, ../includes/Admin/Elementor/Better_Payment_Widget.php:1862, ../includes/Admin/Elementor/Better_Payment_Widget.php:1874, ../includes/Admin/Elementor/Better_Payment_Widget.php:1886, ../includes/Admin/Elementor/Better_Payment_Widget.php:1898, ../includes/Admin/Elementor/Better_Payment_Widget.php:1910, ../includes/Admin/Elementor/Better_Payment_Widget.php:1922, ../includes/Admin/Elementor/Better_Payment_Widget.php:2030, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:81, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:120, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:177, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:284, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:118, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:110, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:124
msgid "No"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:448
msgid "Whoops! It seems like you haven't configured <b>PayPal (Business Email) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:459
msgid "Enable Stripe"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:470, ../includes/Admin/Elementor/Better_Payment_Widget.php:503
msgid "Whoops! It seems like you haven't configured <b>Stripe (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:536
msgid "Enable Paystack"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:550
msgid "Whoops! It seems like you haven't configured <b>Paystack (Public and Secret Key) Settings</b>. Make sure to configure these settings before you publish the form."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:579
msgid "Enable Email Notification"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:591
msgid "Show Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:611
msgid "Use WooCommerce Currency?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:723
msgid "WooCommerce Currency"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:741
msgid "Supported by %sStripe%s only"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:779, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:67, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:68
msgid "Currency Alignment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:783, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:71, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:72
msgid "Left"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:787, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:75, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:76
msgid "Right"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:841
msgid "Form Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:865, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:102
msgid "Form Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:884, ../includes/Admin/Elementor/Better_Payment_Widget.php:886, ../includes/Admin/Elementor/Better_Payment_Widget.php:899
msgid "Field Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:897, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:737, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:754
msgid "Placeholder Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:910, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:243
msgid "Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:914
msgid "Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:915, ../includes/Admin/Elementor/Better_Payment_Widget.php:942, ../includes/Admin/Elementor/Better_Payment_Widget.php:1200, ../includes/Admin/views/better-payment-settings.php:89
msgid "Email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:916
msgid "Number"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:935
msgid "Primary Field Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:936
msgid "If this is a primary field (first name, last name, email etc), then please select one."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:940, ../includes/Admin/Elementor/Better_Payment_Widget.php:1136, ../includes/Admin/Elementor/Better_Payment_Widget.php:1137
msgid "First Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:941, ../includes/Admin/Elementor/Better_Payment_Widget.php:1145, ../includes/Admin/Elementor/Better_Payment_Widget.php:1146
msgid "Last Name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:943, ../includes/Admin/Elementor/Better_Payment_Widget.php:1163, ../includes/Admin/Elementor/Better_Payment_Widget.php:1164, ../includes/Admin/Elementor/Better_Payment_Widget.php:1315, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:29, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:93
msgid "Payment Amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:944
msgid "None"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:953, ../includes/Admin/Elementor/Better_Payment_Widget.php:2167, ../includes/Admin/Elementor/Better_Payment_Widget.php:2430, ../includes/Admin/Elementor/Better_Payment_Widget.php:2871, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1617, ../includes/Admin/Elementor/User_Dashboard.php:643
msgid "Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:954
msgid "Select an icon for this field (not applicable for primary field - Payment Amount and layout 4, 5, 6)."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:963, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:115, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:128, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:143, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:158, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:174, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:190, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:206, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:219, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:235, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:250, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:266, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:282, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:298, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:562, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:577, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:673, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:706, ../includes/Admin/Elementor/User_Dashboard.php:117, ../includes/Admin/Elementor/User_Dashboard.php:129, ../includes/Admin/Elementor/User_Dashboard.php:144, ../includes/Admin/Elementor/User_Dashboard.php:159, ../includes/Admin/Elementor/User_Dashboard.php:171, ../includes/Admin/Elementor/User_Dashboard.php:183, ../includes/Admin/Elementor/User_Dashboard.php:195, ../includes/Admin/Elementor/User_Dashboard.php:1662, ../includes/Admin/Elementor/User_Dashboard.php:1674, ../includes/Admin/Elementor/User_Dashboard.php:1686, ../includes/Admin/Elementor/User_Dashboard.php:1698, ../includes/Admin/Elementor/User_Dashboard.php:1710, ../includes/Admin/Elementor/User_Dashboard.php:1861, ../includes/Admin/Elementor/User_Dashboard.php:1873, ../includes/Admin/Elementor/User_Dashboard.php:1885, ../includes/Admin/Elementor/User_Dashboard.php:1897, ../includes/Admin/Elementor/User_Dashboard.php:1909, ../includes/Admin/Elementor/User_Dashboard.php:1921, ../includes/Admin/Elementor/User_Dashboard.php:1933, ../includes/Admin/Elementor/User_Dashboard.php:1945
msgid "Show"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:978
msgid "Field is hidden if payment source is WooCommerce or payment type is recurring/split payment or field dynamic value is enabled."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:990
msgid "Required"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1005, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:284
msgid "Min. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1016, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:295
msgid "Max. Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1027, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:306
msgid "Default Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1038
msgid "Dynamic Value"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1040
msgid "It will override default value!"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1055
msgid "<p class=\"better-payment-dynamic-value-info\" style=\"word-break: break-word;\"><a href=\"%1$s\" target=\"_blank\">Sample url »</a><br>%1$s</p>"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1072, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:317
msgid "Readonly"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1086
msgid "Display Inline?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1106
msgid "Column Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1131
msgid "Set the width of the column. Use less than 50% to make fields inline"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1154, ../includes/Admin/Elementor/Better_Payment_Widget.php:1155, ../includes/Admin/Elementor/User_Dashboard.php:1871, ../includes/Admin/Elementor/User_Dashboard.php:1991, ../includes/Admin/Elementor/User_Dashboard.php:1994, ../includes/Admin/views/better-payment-transaction-list.php:228, ../includes/Admin/views/template-transaction-list.php:35
msgid "Email Address"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1172, ../includes/Admin/Elementor/Better_Payment_Widget.php:1173
msgid "First name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1186, ../includes/Admin/Elementor/Better_Payment_Widget.php:1187
msgid "Last name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1201
msgid "Enter your email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1210, ../includes/Admin/Elementor/Better_Payment_Widget.php:1219
msgid "Other amount"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1218
msgid "Amount to pay"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1256, ../includes/Admin/Elementor/Better_Payment_Widget.php:1270, ../includes/Admin/Elementor/Better_Payment_Widget.php:1284, ../includes/Admin/Elementor/Better_Payment_Widget.php:1298
msgid "Form Fields"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1324, ../includes/Admin/Elementor/Better_Payment_Widget.php:1340, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:117
msgid "Show Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1356, ../includes/Admin/Elementor/Better_Payment_Widget.php:1386, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:602, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:644, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:248, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:146
msgid "Amount List"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1420, ../includes/Admin/Elementor/Better_Payment_Widget.php:1431
msgid "Transaction Details"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1429
msgid "Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1442
msgid "Sub Heading Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1444
msgid "Total payment of your product in the following:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1455
msgid "Product Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1457
msgid "Title:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1471, ../includes/Admin/Elementor/Better_Payment_Widget.php:2719, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1465
msgid "Amount Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1473, ../includes/Admin/views/better-payment-transaction-view.php:130
msgid "Amount:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1489
msgid "Form Custom Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1498
msgid "Form Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1513
msgid "Form Sub-Title"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1528
msgid "PayPal Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1540
msgid "Stripe Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1552
msgid "Paystack Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1568
msgid "PayPal Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1595, ../includes/Admin/Elementor/Form_Actions/Paypal_Integration.php:101
msgid "Button Type"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1625
msgid "Stripe Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1729
msgid "Paystack Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1743, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:74, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:88
msgid "Public Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1762, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:90, ../includes/Admin/Elementor/Form_Actions/Stripe_Integration.php:104
msgid "Secret Key"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1795
msgid "Email Settings"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1805
msgid "Choose Logo"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1818
msgid "Admin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1822, ../includes/Admin/views/better-payment-settings.php:207, ../includes/Admin/views/better-payment-settings.php:284, ../includes/Admin/views/template-email-notification.php:270
msgid "To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1827
msgid "Email address to notify site admin after each successful transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1837, ../includes/Admin/Elementor/Better_Payment_Widget.php:2004, ../includes/Admin/views/better-payment-settings.php:214, ../includes/Admin/views/better-payment-settings.php:290
msgid "Subject"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1849, ../includes/Admin/Elementor/Better_Payment_Widget.php:2016, ../includes/Admin/views/better-payment-settings.php:221, ../includes/Admin/views/better-payment-settings.php:297
msgid "Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1859
msgid "Show Greeting Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1871
msgid "Show Header Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1883
msgid "Show From Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1895
msgid "Show To Section"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1907
msgid "Show Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1919
msgid "Show Footer Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1932, ../includes/Admin/Elementor/Better_Payment_Widget.php:2074
msgid "From email"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1943, ../includes/Admin/Elementor/Better_Payment_Widget.php:2085
msgid "From name"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1953, ../includes/Admin/Elementor/Better_Payment_Widget.php:2095, ../includes/Admin/views/better-payment-settings.php:245, ../includes/Admin/views/better-payment-settings.php:321
msgid "Reply-To"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1964, ../includes/Admin/Elementor/Better_Payment_Widget.php:2106, ../includes/Admin/views/better-payment-settings.php:252, ../includes/Admin/views/better-payment-settings.php:328
msgid "Cc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1974, ../includes/Admin/Elementor/Better_Payment_Widget.php:2116, ../includes/Admin/views/better-payment-settings.php:259, ../includes/Admin/views/better-payment-settings.php:335
msgid "Bcc"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1984, ../includes/Admin/Elementor/Better_Payment_Widget.php:2126
msgid "Send as"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1989, ../includes/Admin/Elementor/Better_Payment_Widget.php:2131
msgid "HTML"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1990, ../includes/Admin/Elementor/Better_Payment_Widget.php:2132, ../includes/Admin/views/better-payment-settings.php:271, ../includes/Admin/views/better-payment-settings.php:347
msgid "Plain"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:1998
msgid "Customer"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2026
msgid "PDF Attachment?"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2038
msgid "Attachment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2039
msgid "Allowed file types: jpg, jpeg, png"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2054
msgid "PDF"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2055
msgid "Allowed file types: pdf"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2147
msgid "Success Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2176, ../includes/Admin/Elementor/User_Dashboard.php:213
msgid "Content"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2183
msgid "Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2185, ../includes/Admin/Elementor/Better_Payment_Widget.php:2186, ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "Use shortcode like"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "to customize your message."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "eg:"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2187
msgid "for your order."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2192
msgid "Sub Heading"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2194, ../includes/Admin/Elementor/Better_Payment_Widget.php:2195, ../includes/Admin/Elementor/Better_Payment_Widget.php:2196
msgid "Payment confirmation email will be sent to"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2203
msgid "Transaction"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2220
msgid "Thank You"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2222, ../includes/Admin/Elementor/Better_Payment_Widget.php:2223
msgid "Thank you for your payment"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2389
msgid "User Dashboard URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2391, ../includes/Admin/Elementor/Better_Payment_Widget.php:2399, ../includes/Admin/Elementor/Better_Payment_Widget.php:2456
msgid "eg. https://example.com/custom-page/"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2392
msgid "Please enter the page url where <strong>User Dashboard</strong> widget is used."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2397, ../includes/Admin/Elementor/Better_Payment_Widget.php:2454
msgid "Custom Redirect URL"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2400, ../includes/Admin/Elementor/Better_Payment_Widget.php:2457
msgid "Please note that only your current domain is allowed here to keep your site secure."
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2410
msgid "Error Message"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2439
msgid "Heading Message Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2477
msgid "Form Sidebar Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2494, ../includes/Admin/Elementor/Better_Payment_Widget.php:2613, ../includes/Admin/Elementor/Better_Payment_Widget.php:2687, ../includes/Admin/Elementor/Better_Payment_Widget.php:2761, ../includes/Admin/Elementor/Better_Payment_Widget.php:2837, ../includes/Admin/Elementor/Better_Payment_Widget.php:2923, ../includes/Admin/Elementor/Better_Payment_Widget.php:2974, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1359, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1433, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1507, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1583, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1669, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1720, ../includes/Admin/Elementor/User_Dashboard.php:308, ../includes/Admin/Elementor/User_Dashboard.php:446, ../includes/Admin/Elementor/User_Dashboard.php:694, ../includes/Admin/Elementor/User_Dashboard.php:857, ../includes/Admin/Elementor/User_Dashboard.php:978, ../includes/Admin/Elementor/User_Dashboard.php:1131
msgid "Margin"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2510, ../includes/Admin/Elementor/Better_Payment_Widget.php:2630, ../includes/Admin/Elementor/Better_Payment_Widget.php:2703, ../includes/Admin/Elementor/Better_Payment_Widget.php:2778, ../includes/Admin/Elementor/Better_Payment_Widget.php:2854, ../includes/Admin/Elementor/Better_Payment_Widget.php:2938, ../includes/Admin/Elementor/Better_Payment_Widget.php:2989, ../includes/Admin/Elementor/Better_Payment_Widget.php:3120, ../includes/Admin/Elementor/Better_Payment_Widget.php:3740, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1376, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1449, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1524, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1600, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1684, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1735, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1866, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2486, ../includes/Admin/Elementor/User_Dashboard.php:320, ../includes/Admin/Elementor/User_Dashboard.php:458, ../includes/Admin/Elementor/User_Dashboard.php:706, ../includes/Admin/Elementor/User_Dashboard.php:869, ../includes/Admin/Elementor/User_Dashboard.php:990, ../includes/Admin/Elementor/User_Dashboard.php:1143
msgid "Padding"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2525, ../includes/Admin/Elementor/Better_Payment_Widget.php:3004, ../includes/Admin/Elementor/Better_Payment_Widget.php:3234, ../includes/Admin/Elementor/Better_Payment_Widget.php:3609, ../includes/Admin/Elementor/Better_Payment_Widget.php:3725, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1750, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1980, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2355, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2471, ../includes/Admin/Elementor/User_Dashboard.php:333, ../includes/Admin/Elementor/User_Dashboard.php:482, ../includes/Admin/Elementor/User_Dashboard.php:719, ../includes/Admin/Elementor/User_Dashboard.php:882, ../includes/Admin/Elementor/User_Dashboard.php:1003, ../includes/Admin/Elementor/User_Dashboard.php:1156, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:266, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:471
msgid "Border Radius"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2561, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1307
msgid "Sidebar Text Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2572, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1318
msgid "Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2583, ../includes/Admin/Elementor/Better_Payment_Widget.php:2659, ../includes/Admin/Elementor/Better_Payment_Widget.php:2731, ../includes/Admin/Elementor/Better_Payment_Widget.php:2807, ../includes/Admin/Elementor/Better_Payment_Widget.php:2883, ../includes/Admin/Elementor/Better_Payment_Widget.php:3343, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1329, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1405, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1477, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1553, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1629, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2089, ../includes/Admin/Elementor/User_Dashboard.php:671, ../includes/Admin/Elementor/User_Dashboard.php:749, ../includes/Admin/Elementor/User_Dashboard.php:793, ../includes/Admin/Elementor/User_Dashboard.php:1033, ../includes/Admin/Elementor/User_Dashboard.php:1078, ../includes/Admin/Elementor/User_Dashboard.php:1186, ../includes/Admin/Elementor/User_Dashboard.php:1233, ../includes/Admin/Elementor/User_Dashboard.php:1299, ../includes/Admin/Elementor/User_Dashboard.php:1327, ../includes/Admin/Elementor/User_Dashboard.php:1367, ../includes/Admin/Elementor/User_Dashboard.php:1395, ../includes/Admin/Elementor/User_Dashboard.php:1435, ../includes/Admin/Elementor/User_Dashboard.php:1463
msgid "Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2601, ../includes/Admin/Elementor/Better_Payment_Widget.php:2676, ../includes/Admin/Elementor/Better_Payment_Widget.php:2750, ../includes/Admin/Elementor/Better_Payment_Widget.php:2826, ../includes/Admin/Elementor/Better_Payment_Widget.php:3251, ../includes/Admin/Elementor/Better_Payment_Widget.php:3625, ../includes/Admin/Elementor/Better_Payment_Widget.php:3778, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1347, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1422, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1496, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1572, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1997, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2371, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2524, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:487
msgid "Typography"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2647, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1393
msgid "Sub-Title Text"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2795, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1541
msgid "Amount Summary"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2898, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1644
msgid "Font Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:2957, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1703
msgid "Form Container Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3040, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1786
msgid "Form Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3048, ../includes/Admin/Elementor/Better_Payment_Widget.php:3524, ../includes/Admin/Elementor/Better_Payment_Widget.php:3563, ../includes/Admin/Elementor/Better_Payment_Widget.php:3685, ../includes/Admin/Elementor/Better_Payment_Widget.php:3804, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1794, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2270, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2309, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2431, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2550, ../includes/Admin/Elementor/User_Dashboard.php:358, ../includes/Admin/Elementor/User_Dashboard.php:391, ../includes/Admin/Elementor/User_Dashboard.php:471, ../includes/Admin/Elementor/User_Dashboard.php:760, ../includes/Admin/Elementor/User_Dashboard.php:804, ../includes/Admin/Elementor/User_Dashboard.php:904, ../includes/Admin/Elementor/User_Dashboard.php:937, ../includes/Admin/Elementor/User_Dashboard.php:1045, ../includes/Admin/Elementor/User_Dashboard.php:1090, ../includes/Admin/Elementor/User_Dashboard.php:1200, ../includes/Admin/Elementor/User_Dashboard.php:1245, ../includes/Admin/Elementor/User_Dashboard.php:1310, ../includes/Admin/Elementor/User_Dashboard.php:1338, ../includes/Admin/Elementor/User_Dashboard.php:1378, ../includes/Admin/Elementor/User_Dashboard.php:1406, ../includes/Admin/Elementor/User_Dashboard.php:1446, ../includes/Admin/Elementor/User_Dashboard.php:1474, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:221, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:380, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:422
msgid "Background Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3063, ../includes/Admin/Elementor/Better_Payment_Widget.php:3539, ../includes/Admin/Elementor/Better_Payment_Widget.php:3578, ../includes/Admin/Elementor/Better_Payment_Widget.php:3700, ../includes/Admin/Elementor/Better_Payment_Widget.php:3820, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1809, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2285, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2324, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2446, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2566, ../includes/Admin/Elementor/User_Dashboard.php:556, ../includes/Admin/Elementor/User_Dashboard.php:573, ../includes/Admin/Elementor/User_Dashboard.php:611, ../includes/Admin/Elementor/User_Dashboard.php:628, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:190, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:395, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:437
msgid "Text Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3079, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1825
msgid "Placeholder Color"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3094, ../includes/Admin/Elementor/Better_Payment_Widget.php:3489, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1840, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2235, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:342
msgid "Spacing"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3135, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1881
msgid "Text Indent"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3162, ../includes/Admin/Elementor/Better_Payment_Widget.php:3445, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1908, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2191, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:298
msgid "Input Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3172, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1918
msgid "Set width for all input fields. Not applicable if the field is set to display inline (<b>Content => Form Settings => Form Fields (Repeater) => Display Inline?</b>)"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3193, ../includes/Admin/Elementor/Better_Payment_Widget.php:3467, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1939, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2213, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:320
msgid "Input Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3224, ../includes/Admin/Elementor/Better_Payment_Widget.php:3292, ../includes/Admin/Elementor/Better_Payment_Widget.php:3316, ../includes/Admin/Elementor/Better_Payment_Widget.php:3598, ../includes/Admin/Elementor/Better_Payment_Widget.php:3716, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1970, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2038, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2062, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2344, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2462, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:457
msgid "Border"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3281, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2027
msgid "Active"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3305, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2051
msgid "Inactive"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3331, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2077
msgid "Input Icon"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3362, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2108, ../includes/Admin/Elementor/User_Dashboard.php:523, ../includes/Admin/Elementor/User_Dashboard.php:652
msgid "Size"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3386, ../includes/Admin/Elementor/Better_Payment_Widget.php:3654, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2132, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2400
msgid "Width"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3409, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2155
msgid "Height"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3437, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2183
msgid "Amount Fields Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3517, ../includes/Admin/Elementor/Better_Payment_Widget.php:3678, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2263, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2424, ../includes/Admin/Elementor/User_Dashboard.php:352, ../includes/Admin/Elementor/User_Dashboard.php:550, ../includes/Admin/Elementor/User_Dashboard.php:605, ../includes/Admin/Elementor/User_Dashboard.php:743, ../includes/Admin/Elementor/User_Dashboard.php:898, ../includes/Admin/Elementor/User_Dashboard.php:1027, ../includes/Admin/Elementor/User_Dashboard.php:1180, ../includes/Admin/Elementor/User_Dashboard.php:1293, ../includes/Admin/Elementor/User_Dashboard.php:1361, ../includes/Admin/Elementor/User_Dashboard.php:1429, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:370
msgid "Normal"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3556, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2302, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:412
msgid "Selected"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3646, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2392
msgid "Form Button Style"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3755, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2501
msgid "Margin Top"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3797, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2543, ../includes/Admin/Elementor/User_Dashboard.php:385, ../includes/Admin/Elementor/User_Dashboard.php:567, ../includes/Admin/Elementor/User_Dashboard.php:622, ../includes/Admin/Elementor/User_Dashboard.php:787, ../includes/Admin/Elementor/User_Dashboard.php:931, ../includes/Admin/Elementor/User_Dashboard.php:1072, ../includes/Admin/Elementor/User_Dashboard.php:1227, ../includes/Admin/Elementor/User_Dashboard.php:1321, ../includes/Admin/Elementor/User_Dashboard.php:1389, ../includes/Admin/Elementor/User_Dashboard.php:1457
msgid "Hover"
msgstr ""

#: ../includes/Admin/Elementor/Better_Payment_Widget.php:3836, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:2582, ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:236
msgid "Border Color"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:59
msgid "Fundraising Campaign"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:96, ../includes/Admin/views/better-payment-settings.php:86
msgid "General"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:103
msgid "Campaign Layout"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:113, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:324, ../includes/Admin/Elementor/User_Dashboard.php:193, ../includes/Admin/Elementor/User_Dashboard.php:686
msgid "Header"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:116, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:129, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:144, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:159, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:175, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:191, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:207, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:220, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:236, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:251, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:267, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:283, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:299, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:563, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:578, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:674, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:707, ../includes/Admin/Elementor/User_Dashboard.php:118, ../includes/Admin/Elementor/User_Dashboard.php:130, ../includes/Admin/Elementor/User_Dashboard.php:145, ../includes/Admin/Elementor/User_Dashboard.php:160, ../includes/Admin/Elementor/User_Dashboard.php:172, ../includes/Admin/Elementor/User_Dashboard.php:184, ../includes/Admin/Elementor/User_Dashboard.php:196, ../includes/Admin/Elementor/User_Dashboard.php:1663, ../includes/Admin/Elementor/User_Dashboard.php:1675, ../includes/Admin/Elementor/User_Dashboard.php:1687, ../includes/Admin/Elementor/User_Dashboard.php:1699, ../includes/Admin/Elementor/User_Dashboard.php:1711, ../includes/Admin/Elementor/User_Dashboard.php:1862, ../includes/Admin/Elementor/User_Dashboard.php:1874, ../includes/Admin/Elementor/User_Dashboard.php:1886, ../includes/Admin/Elementor/User_Dashboard.php:1898, ../includes/Admin/Elementor/User_Dashboard.php:1910, ../includes/Admin/Elementor/User_Dashboard.php:1922, ../includes/Admin/Elementor/User_Dashboard.php:1934, ../includes/Admin/Elementor/User_Dashboard.php:1946
msgid "Hide"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:126, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:334, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:463, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:480, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:810, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:995, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1071, ../includes/Admin/Elementor/Controls/Select2.php:30
msgid "Title"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:141, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:347
msgid "Short Description"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:156
msgid "Image One"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:172
msgid "Image Two"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:188
msgid "Image Three"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:204, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:800, ../includes/Admin/views/partials/campaign-vars.php:136, ../includes/Admin/views/partials/campaign-vars.php:98
msgid "Overview"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:217, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:360, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:873
msgid "Images"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:233
msgid "Description One"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:248
msgid "Description Two"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:264, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:982, ../includes/Admin/views/partials/campaign-vars.php:116
msgid "Our Mission"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:280, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1060
msgid "Our Team"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:296, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1157
msgid "Related Campaigns"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:340
msgid "Campaign header title"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:353
msgid "Campaign header short description"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:370, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:888
msgid "First"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:380, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:407, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:434, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:514, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:900, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:931, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:962, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1102
msgid "Choose Image"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:397, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:919
msgid "Second"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:424, ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:950
msgid "Third"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:456
msgid "Form"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:497
msgid "Sub Title"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:532
msgid "Goal Amount Label"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:549
msgid "Goal Amount"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:560
msgid "Goal Percentage"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:575
msgid "Goal Bar Line"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:671
msgid "Total Donation"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:686
msgid "Total Donation Label"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:704
msgid "Raised Amount"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:719
msgid "Raised Amount Label"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:771
msgid "Button Text"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:785
msgid "Button Link"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:788
msgid "https://example.com/custom-page/"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:789
msgid "Please enter the donation form page url here."
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:826
msgid "Descriptions"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:838
msgid "Short Description 1"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:855
msgid "Short Description 2"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1014
msgid "Mission"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1027
msgid "Missions"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1090, ../includes/Admin/Elementor/User_Dashboard.php:1859, ../includes/Admin/Elementor/User_Dashboard.php:1978, ../includes/Admin/Elementor/User_Dashboard.php:1981, ../includes/Admin/views/template-transaction-list.php:32
msgid "Name"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1113
msgid "Team Members"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1169
msgid "Campaign ID"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1182
msgid "Campaigns"
msgstr ""

#: ../includes/Admin/Elementor/Fundraising_Campaign_Widget.php:1223
msgid "Campaign Header Style"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:58, ../includes/Admin/views/better-payment-settings.php:158
msgid "User Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:98, ../includes/Admin/Elementor/User_Dashboard.php:105
msgid "Layout"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:115, ../includes/Admin/Elementor/User_Dashboard.php:426
msgid "Sidebar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:127, ../includes/Admin/Elementor/User_Dashboard.php:514
msgid "Avatar"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:142
msgid "Username"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:157, ../includes/Admin/Elementor/User_Dashboard.php:228, ../includes/Admin/Elementor/User_Dashboard.php:231, ../includes/Admin/Elementor/User_Dashboard.php:1651, ../includes/Admin/Elementor/User_Dashboard.php:1722
msgid "Dashboard"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:181, ../includes/Admin/Elementor/User_Dashboard.php:249, ../includes/Admin/Elementor/User_Dashboard.php:252
msgid "Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:220
msgid "Label"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:259, ../includes/Admin/Elementor/User_Dashboard.php:262, ../includes/Admin/views/page-analytics.php:13
msgid "Refresh Stats"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:269
msgid "No Items"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:272, ../includes/Admin/views/better-payment-transaction-view.php:243, ../includes/Admin/views/template-transaction-list.php:161
msgid "No records found!"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:300
msgid "Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:437
msgid "Sidebar Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:588
msgid "Menu"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:840
msgid "Table"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:849
msgid "Table Container"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:969
msgid "Table Header"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1122
msgid "Table Body"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1276
msgid "Table Body » Buttons"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1285
msgid "Active Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1353
msgid "Inactive Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1421
msgid "Cancel Button"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1660
msgid "Transaction Summary"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1672
msgid "Analytics Report"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1684, ../includes/Admin/Elementor/User_Dashboard.php:1809, ../includes/Admin/Elementor/User_Dashboard.php:1812
msgid "Recent Transactions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1696
msgid "Recurring Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1708
msgid "Split Subscription"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1731, ../includes/Admin/Elementor/User_Dashboard.php:1734, ../includes/Admin/views/template-email-notification.php:255
msgid "Total Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1744, ../includes/Admin/Elementor/User_Dashboard.php:1747
msgid "Completed Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1757, ../includes/Admin/Elementor/User_Dashboard.php:1760
msgid "Incomplete Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1770, ../includes/Admin/Elementor/User_Dashboard.php:1773
msgid "Refunded Amount"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1783, ../includes/Admin/Elementor/User_Dashboard.php:1786
msgid "View All"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1796, ../includes/Admin/Elementor/User_Dashboard.php:1799
msgid "Analytics Reports"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1822, ../includes/Admin/Elementor/User_Dashboard.php:1825
msgid "Recurring Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1835, ../includes/Admin/Elementor/User_Dashboard.php:1838
msgid "Split Subscriptions"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1850, ../includes/Admin/Elementor/User_Dashboard.php:1969
msgid "Transactions List"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1919, ../includes/Admin/Elementor/User_Dashboard.php:2043, ../includes/Admin/Elementor/User_Dashboard.php:2046, ../includes/Admin/views/better-payment-transaction-list.php:272, ../includes/Admin/views/better-payment-transaction-list.php:275, ../includes/Admin/views/template-transaction-list.php:47
msgid "Source"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1931, ../includes/Admin/Elementor/User_Dashboard.php:2056, ../includes/Admin/Elementor/User_Dashboard.php:2059, ../includes/Admin/views/better-payment-transaction-list.php:179, ../includes/Admin/views/better-payment-transaction-list.php:198, ../includes/Admin/views/template-transaction-list.php:50
msgid "Status"
msgstr ""

#: ../includes/Admin/Elementor/User_Dashboard.php:1943, ../includes/Admin/Elementor/User_Dashboard.php:2069, ../includes/Admin/Elementor/User_Dashboard.php:2072, ../includes/Admin/views/template-transaction-list.php:53
msgid "Date"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:51
msgid "Save Changes"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:69
msgid "Go Premium"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:75
msgid "License"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:91
msgid "Admin Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:92
msgid "Customer Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:96
msgid "Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:100, ../includes/Admin/views/better-payment-settings.php:141, ../includes/Admin/views/better-payment-transaction-list.php:278, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:29, ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:45, ../includes/Admin/views/elementor/layouts/layout-1.php:98, ../includes/Admin/views/elementor/layouts/layout-2.php:66
msgid "Paystack"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:112, ../includes/Admin/views/better-payment-settings.php:128, ../includes/Admin/views/better-payment-settings.php:144, ../includes/Admin/views/better-payment-settings.php:161
msgid "See documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:143
msgid "Enable Paystack if you want to accept payment via Paystack."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:160
msgid "Enable User Dashboard widget. It shows list of transactions and subscriptions for the user."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:209
msgid "Email address"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:210
msgid "Enter website admin email address here. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:216, ../includes/Admin/views/better-payment-settings.php:292
msgid "Email subject"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:217
msgid "Email subject for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:224
msgid "Email body for the admin email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:228, ../includes/Admin/views/better-payment-settings.php:304
msgid "Additional Headers"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:231, ../includes/Admin/views/better-payment-settings.php:307
msgid "From Name"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:234, ../includes/Admin/views/better-payment-settings.php:310
msgid "From name that will be used in the email headers."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:238, ../includes/Admin/views/better-payment-settings.php:314
msgid "From Email"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:241, ../includes/Admin/views/better-payment-settings.php:317
msgid "Email address that will be displayed in the email header as From Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:248, ../includes/Admin/views/better-payment-settings.php:324
msgid "Email address that will be displayed in the email header as Reply-To Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:255, ../includes/Admin/views/better-payment-settings.php:331
msgid "Email address that will be displayed in the email header as Cc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:262, ../includes/Admin/views/better-payment-settings.php:338
msgid "Email address that will be displayed in the email header as Bcc Email."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:266, ../includes/Admin/views/better-payment-settings.php:342
msgid "Send As"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:270, ../includes/Admin/views/better-payment-settings.php:346
msgid "Select One"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:272, ../includes/Admin/views/better-payment-settings.php:348
msgid "Html"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:275, ../includes/Admin/views/better-payment-settings.php:351
msgid "Html helps to send html markup in the email body. Select plain if you just want plain text in the email body."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:286
msgid "Customer email address will be auto populated from payment form. This email will be used to send email notification for each transaction."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:293
msgid "Email subject for the customer (who make payments) email notification."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:300
msgid "Email body for the customer email notification. "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:386
msgid "Live Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:388
msgid "PayPal live client ID is required to do Refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:389, ../includes/Admin/views/better-payment-settings.php:401, ../includes/Admin/views/better-payment-settings.php:413, ../includes/Admin/views/better-payment-settings.php:425, ../includes/Admin/views/better-payment-settings.php:459, ../includes/Admin/views/better-payment-settings.php:471, ../includes/Admin/views/better-payment-settings.php:483, ../includes/Admin/views/better-payment-settings.php:495, ../includes/Admin/views/better-payment-settings.php:528, ../includes/Admin/views/better-payment-settings.php:540, ../includes/Admin/views/better-payment-settings.php:552, ../includes/Admin/views/better-payment-settings.php:564
msgid "see documentation."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:398
msgid "Live Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:400
msgid "PayPal live secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:410
msgid "Test/Sandbox Client ID"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:412
msgid "PayPal test/sandbox client id is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:422
msgid "Test/Sandbox Secret"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:424
msgid "PayPal test/sandbox secret is required to do refund via PayPal. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:510
msgid "Live mode allows you to process real transactions. It just requires live Paystack keys (public and secret keys) to accept real payments."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:527
msgid "Paystack live public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:539
msgid "Paystack live secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:551
msgid "Paystack test public key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:563
msgid "Paystack test secret key is required to make payments via Paystack. For more help visit"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:611, ../includes/Admin/views/better-payment-transaction-list.php:123, ../includes/Admin/views/page-analytics.php:30
msgid "Total Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:620, ../includes/Admin/views/better-payment-transaction-list.php:135, ../includes/Admin/views/page-analytics.php:49
msgid "Completed Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:629, ../includes/Admin/views/better-payment-transaction-list.php:147, ../includes/Admin/views/page-analytics.php:68
msgid "Incomplete Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:641, ../includes/Admin/views/better-payment-settings.php:643
msgid "Documentation"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:642
msgid "Get started by spending some time with the documentation to get familiar with Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:653
msgid "Contribute to Better Payment"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:654
msgid "You can contribute to make Better Payment better reporting bugs, creating issues, pull requests at "
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:655
msgid "Report a Bug"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:665
msgid "Need Help?"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:666
msgid "Stuck with something? Get help from live chat or support ticket."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:667
msgid "Initiate a Chat"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:676
msgid "Show Your Love"
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:679
msgid "We love to have you in Better Payment family. We are making it more awesome everyday. Take your 2 minutes to review the plugin and spread the love to encourage us to keep it going."
msgstr ""

#: ../includes/Admin/views/better-payment-settings.php:680
msgid "Leave a Review"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:38
msgid "Import Data"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:39
msgid "Export All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:63
msgid "Choose csv file…"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:67
msgid "No file chosen"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:73
msgid "Let's Go!"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:78
msgid "Upload any csv file that is exported from another site via Better Payment."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:159, ../includes/Admin/views/better-payment-transaction-list.php:161, ../includes/Admin/Elementor/Controls/Select2.php:27
msgid "Search"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:161
msgid "Search by email, amount, transaction id, source"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:165
msgid "From Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:167
msgid "Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:172, ../includes/Admin/views/better-payment-transaction-list.php:174
msgid "To Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:182, ../includes/Admin/views/better-payment-transaction-list.php:199
msgid "All"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:200
msgid "Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:201
msgid "Incomplete"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:202
msgid "Refunded"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:223, ../includes/Admin/views/better-payment-transaction-list.php:226
msgid "Sort By"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:227
msgid "Payment Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:250, ../includes/Admin/views/better-payment-transaction-list.php:253
msgid "Sort Order"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:254
msgid "Descending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:255
msgid "Ascending"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:295
msgid "Filter"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:296
msgid "Reset"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:303
msgid "Custom Date Range"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:311
msgid "Start Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:316
msgid "Select start date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:324
msgid "End Date"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:329
msgid "Select end date of desired time period to see the analytics."
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:337
msgid "Confirm"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-list.php:338
msgid "Cancel"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:41
msgid "Back to Transactions"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:54, ../includes/Admin/views/better-payment-transaction-view.php:55, ../includes/Admin/views/better-payment-transaction-view.php:58, ../includes/Admin/views/better-payment-transaction-view.php:62, ../includes/Admin/views/better-payment-transaction-view.php:65, ../includes/Admin/views/better-payment-transaction-view.php:67
msgid ""
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:82, ../includes/Admin/views/better-payment-transaction-view.php:96, ../includes/Admin/views/better-payment-transaction-view.php:97, ../includes/Admin/views/template-transaction-list.php:142, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:168
msgid "N/A"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:98
msgid "#"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:111
msgid "Basic Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:118
msgid "Name:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:120
msgid "Email Address:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:122, ../includes/Admin/views/better-payment-transaction-view.php:139, ../includes/Admin/views/better-payment-transaction-view.php:211, ../includes/Admin/views/template-transaction-list.php:104, ../includes/Admin/views/template-transaction-list.php:121, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:122, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:143
msgid "Copy"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:126
msgid "Single Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:127
msgid "Quantity:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:128
msgid "Total Amount:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:135, ../includes/Admin/views/better-payment-transaction-view.php:209
msgid "Transaction ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:145
msgid "Source:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:147
msgid "Status:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:156
msgid "Mark as Completed"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:163
msgid "Additional Information"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:166
msgid "Order ID:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:167
msgid "Payment Date:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:168
msgid "Referer Page:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:169
msgid "Referer Widget:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:179
msgid "Payment Gateway"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:201, ../includes/Admin/views/better-payment-transaction-view.php:194, ../includes/Admin/views/better-payment-transaction-view.php:187
msgid "Payment Method:"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:216
msgid "Email Activity"
msgstr ""

#: ../includes/Admin/views/better-payment-transaction-view.php:221
msgid "Email sent to"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:13
msgid "We are caching all data for 1 hour. To see the live data press this button!"
msgstr ""

#: ../includes/Admin/views/page-analytics.php:87
msgid "Refund Transactions"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:166
msgid "email-tick"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:174
msgid "This is to acknowledge that we have received the payment of %s %s on %s"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:169
msgid "Great News! Admin"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:170
msgid "You have received a new transaction through"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:192
msgid "Transaction ID - "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:197
msgid "Date : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:213
msgid "From"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:276
msgid "Payment Method : "
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:308
msgid "Transaction Summary:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:319
msgid "Description"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:322
msgid "Rate"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:325
msgid "Qty"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:361
msgid "Total:"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:376
msgid "You can also find the transaction details by visiting the link below."
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:377
msgid "View Transaction"
msgstr ""

#: ../includes/Admin/views/template-email-notification.php:381
msgid "Powered By"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:56
msgid "Action"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:99, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:116
msgid "Imported"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:152, ../includes/Admin/views/elementor/user-dashboard/template-transactions-tab.php:184
msgid "View"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:153
msgid "Delete"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:174
msgid "10"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:175
msgid "20"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:176
msgid "50"
msgstr ""

#: ../includes/Admin/views/template-transaction-list.php:177
msgid "100"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:28
msgid "Remove"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:29
msgid "Image"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:31
msgid "Price"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:32
msgid "Quantity"
msgstr ""

#: ../includes/Admin/Elementor/Controls/Select2.php:33
msgid "Subtotal"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:246
msgid "Both"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:247
msgid "Input Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:261
msgid "Don't forget to enable the <strong>Payment Amount</strong> (& Show Amount List) field from Better Payment Section below"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:273
msgid "Placeholder"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:348
msgid "The value must be less than or equal to %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Field.php:352
msgid "The value must be greater than or equal %s"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:56
msgid "Don't forget to add PayPal or Stripe on <strong>Actions After Submit</strong>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:77
msgid "Payment Amount Field"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:78
msgid "We add an extra field type <b>Payment Amount</b> which offers you to accept payment via Paypal and Stripe. Disable it if you want to hide the field type.<br><br>"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:134
msgid "Form Fields => Payment Amount => <b>Field Type</b> helps to show Amount List with or without Input field."
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:174
msgid "Field Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:250
msgid "Border Width"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Payment_Amount_Integration.php:281
msgid "Amount List Style"
msgstr ""

#: ../includes/Admin/Elementor/Form_Actions/Paystack_Integration.php:58
msgid "Currency Symbols"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:52
msgid "Building Bridges to Change Lives"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:55
msgid "We believe every animal deserves a happy and healthy life. By contributing to our fundraiser, you're supporting rescue operations, medical treatments, and rehoming efforts for animals in desperate need."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:36
msgid "Give Gaza's Children a Chance for Safety and Brighter Future"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:39
msgid "Your generosity can transform lives. Your small donation token can provide food, shelter, medical aid and safety to children in urgent need."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:23
msgid "Give Hope, Change Lives: Help Child in Need"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:26
msgid "Every child deserves a life free from hunger. Your contribution can provide nutritious meals, hope and a brighter future for needy children."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:70
msgid "Donate Now"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:143
msgid "Lending Hands, Healing Hearts"
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:146
msgid ""
"Every individual deserves hope, compassion and a chance at a better tomorrow. Your support helps provide essential resources such as food, clothing and shelter to those whose are in need. Together, we can create opportunities for education, skill-building and empowerment.\n"
"        <br /> <br />\n"
"        Extend a spiritual and humanitarian helping hand to those in desperate need and ensure no one is left behind. Join us in building a world where every soul can thrive. Your kindness can make all the difference. Through your generosity, we can fund critical programs to help underprivileged and senior citizens."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:151
msgid "From providing warm meals and safe places to stay, to educational initiatives and healthcare services, every humanitarian contribution brings us closer to ending the cycle of poverty and inequality. By empowering underprivileged and senior citizens, we uplift entire communities around the world."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:139
msgid "Children in Gaza are facing unimaginable hardships, but your support can provide a lifeline. Every donation, big or small, helps provide essentials like food, shelter, and medical care. Your generosity can transform lives, offering hope and safety to those in need."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:101
msgid "We believe every animal deserves a happy and healthy life. By contributing to our fundraiser, you're supporting rescue operations, medical treatments, and rehoming efforts for animals in desperate need. and rehoming efforts for animals in desperate need."
msgstr ""

#: ../includes/Admin/views/partials/campaign-vars.php:104
msgid "Your generosity fuels our vision of a world where every child has the chance to grow, learn, and thrive without the shadow of hunger."
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:4
msgid "Why upgrade to Premium Version?"
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:5
msgid "Get access to Analytics, Refund, Invoice & many more features that makes your life way easier. You will also get world class support from our dedicated team 24/7."
msgstr ""

#: ../includes/Admin/views/partials/go-premium.php:6
msgid "Get Premium Version"
msgstr ""
