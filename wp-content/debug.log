[29-May-2025 06:11:52 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[29-May-2025 06:16:44 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: syntax error, unexpected token "return" in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:339
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
[29-May-2025 06:22:47 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: Unclosed '[' on line 348 does not match '}' in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:351
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
[29-May-2025 08:19:12 UTC] Automatic updates starting...
[29-May-2025 08:19:13 UTC]   Automatic plugin updates starting...
[29-May-2025 08:19:13 UTC]   Automatic plugin updates complete.
[29-May-2025 08:19:14 UTC]   Automatic theme updates starting...
[29-May-2025 08:19:14 UTC]   Automatic theme updates complete.
[29-May-2025 08:19:14 UTC] Automatic updates complete.
[29-May-2025 09:06:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 02:32:36 UTC] PHP Warning:  wp_version_check(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 02:32:37 UTC] Automatic updates starting...
[01-Jun-2025 02:32:38 UTC]   Automatic plugin updates starting...
[01-Jun-2025 02:32:38 UTC]   Automatic plugin updates complete.
[01-Jun-2025 02:32:39 UTC]   Automatic theme updates starting...
[01-Jun-2025 02:32:39 UTC]   Automatic theme updates complete.
[01-Jun-2025 02:32:39 UTC] Automatic updates complete.
[01-Jun-2025 02:32:44 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 03:39:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[01-Jun-2025 08:20:19 UTC] Automatic updates starting...
[01-Jun-2025 08:20:20 UTC]   Automatic plugin updates starting...
[01-Jun-2025 08:20:20 UTC]   Automatic plugin updates complete.
[01-Jun-2025 08:20:21 UTC]   Automatic theme updates starting...
[01-Jun-2025 08:20:21 UTC]   Automatic theme updates complete.
[01-Jun-2025 08:20:21 UTC] Automatic updates complete.
[01-Jun-2025 09:01:29 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 03:10:54 UTC] Automatic updates starting...
[02-Jun-2025 03:10:55 UTC]   Automatic plugin updates starting...
[02-Jun-2025 03:10:55 UTC]   Automatic plugin updates complete.
[02-Jun-2025 03:10:56 UTC]   Automatic theme updates starting...
[02-Jun-2025 03:10:56 UTC]   Automatic theme updates complete.
[02-Jun-2025 03:10:56 UTC] Automatic updates complete.
[02-Jun-2025 08:18:55 UTC] Automatic updates starting...
[02-Jun-2025 08:18:56 UTC]   Automatic plugin updates starting...
[02-Jun-2025 08:18:56 UTC]   Automatic plugin updates complete.
[02-Jun-2025 08:18:57 UTC]   Automatic theme updates starting...
[02-Jun-2025 08:18:57 UTC]   Automatic theme updates complete.
[02-Jun-2025 08:18:57 UTC] Automatic updates complete.
[02-Jun-2025 08:59:45 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 21:46:38 UTC] Automatic updates starting...
[02-Jun-2025 21:46:39 UTC]   Automatic plugin updates starting...
[02-Jun-2025 21:46:39 UTC]   Automatic plugin updates complete.
[02-Jun-2025 22:03:48 UTC] PHP Warning:  wp_update_themes(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[02-Jun-2025 22:03:49 UTC]   Automatic theme updates starting...
[02-Jun-2025 22:03:49 UTC]   Automatic theme updates complete.
[02-Jun-2025 22:03:49 UTC] Automatic updates complete.
[02-Jun-2025 22:18:57 UTC] PHP Warning:  wp_update_plugins(): An unexpected error occurred. Something may be wrong with WordPress.org or this server&#8217;s configuration. If you continue to have problems, please try the <a href="https://wordpress.org/support/forums/">support forums</a>. (WordPress could not establish a secure connection to WordPress.org. Please contact your server administrator.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:39:50 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:40:20 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 03:40:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>better-payment-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Sites/bp/wp-includes/functions.php on line 6121
[03-Jun-2025 04:52:15 UTC] PHP Fatal error:  During class fetch: Uncaught ParseError: Unclosed '[' on line 375 does not match '}' in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Traits/Helper.php:384
Stack trace:
#0 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#1 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php(19): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#2 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#3 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#4 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Admin.php(27): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#5 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(576): include('/Users/<USER>')
#6 /Users/<USER>/Sites/bp/wp-content/plugins/query-monitor/vendor/composer/ClassLoader.php(427): Composer\Autoload\{closure}('/Users/<USER>')
#7 /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/better-payment.php(181): Composer\Autoload\ClassLoader->loadClass('Better_Payment\\...')
#8 /Users/<USER>/Sites/bp/wp-settings.php(545): include_once('/Users/<USER>')
#9 /Users/<USER>/Sites/bp/wp-config.php(102): require_once('/Users/<USER>')
#10 /Users/<USER>/Sites/bp/wp-load.php(50): require_once('/Users/<USER>')
#11 /Users/<USER>/Sites/bp/wp-admin/admin-ajax.php(22): require_once('/Users/<USER>')
#12 /Users/<USER>/.composer/vendor/laravel/valet/server.php(110): require('/Users/<USER>')
#13 {main} in /Users/<USER>/Sites/bp/wp-content/plugins/better-payment/includes/Controller.php on line 19
